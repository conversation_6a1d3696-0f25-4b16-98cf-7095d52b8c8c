{"name": "<PERSON><PERSON><PERSON><PERSON>", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"axios": "^0.21.4", "core-js": "^3.6.5", "js-base64": "^3.7.2", "mux.js": "^5.14.0", "sass": "^1.32.0", "qs": "^6.10.1", "vant": "^2.12.27", "video.js": "^7.15.4", "videojs-contrib-hls": "^5.15.0", "vue": "^2.6.11", "vue-i18n": "^8.28.1", "vue-router": "^3.5.2", "vuex": "^3.6.2"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-service": "~4.5.0", "babel-eslint": "^10.1.0", "css-loader": "^6.2.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "less": "^4.1.1", "less-loader": "^7.3.0", "postcss-px-to-viewport": "^1.1.1", "postcss-pxtorem": "^6.0.0", "sass-loader": "^10.0.0", "style-loader": "^3.2.1", "stylus": "^0.55.0", "stylus-loader": "^6.1.0", "swiper": "^6.3.5", "vue-awesome-swiper": "^3.1.3", "vue-template-compiler": "^2.6.11"}, "eslintConfig": {"root": true, "env": {"node": true}, "extends": ["plugin:vue/essential", "eslint:recommended"], "parserOptions": {"parser": "babel-es<PERSON>"}, "rules": {}}, "browserslist": ["> 1%", "last 2 versions", "not dead"], "description": "## Project setup ``` yarn install ```", "main": "babel.config.js", "keywords": [], "author": "", "license": "ISC"}