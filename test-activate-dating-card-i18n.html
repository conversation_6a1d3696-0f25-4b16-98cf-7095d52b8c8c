<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>激活约会卡 - 多语言测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 2.5em;
            font-weight: 300;
        }
        
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 1.1em;
        }
        
        .test-container {
            padding: 30px;
            border-bottom: 1px solid #eee;
        }
        
        .test-container:last-child {
            border-bottom: none;
        }
        
        .test-container h2 {
            color: #333;
            margin-top: 0;
            font-size: 1.8em;
            border-left: 4px solid #f5576c;
            padding-left: 15px;
        }
        
        .test-container h3 {
            color: #555;
            margin-top: 25px;
            font-size: 1.3em;
        }
        
        .test-container h4 {
            color: #666;
            margin-top: 20px;
            font-size: 1.1em;
        }
        
        .language-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        
        .language-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            transition: transform 0.2s ease;
        }
        
        .language-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        
        .language-name {
            font-weight: bold;
            color: #f5576c;
            font-size: 1.1em;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        
        .language-name::before {
            content: "🌍";
            margin-right: 8px;
        }
        
        .translation-text {
            font-size: 1.2em;
            color: #333;
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #f5576c;
            font-weight: 500;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin: 20px 0;
        }
        
        .highlight-box h4 {
            margin-top: 0;
            color: white;
        }
        
        .api-data {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            padding: 20px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 0.9em;
            line-height: 1.4;
            overflow-x: auto;
            margin: 15px 0;
        }
        
        .summary {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            padding: 30px;
            text-align: center;
        }
        
        .summary h3 {
            color: #333;
            margin-top: 0;
        }
        
        .summary ul {
            text-align: left;
            display: inline-block;
            margin: 20px 0;
        }
        
        .summary li {
            margin: 8px 0;
            color: #555;
        }
        
        code {
            background: #f1f3f4;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            color: #d73a49;
        }
        
        ul {
            padding-left: 20px;
        }
        
        li {
            margin: 5px 0;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🎯 激活约会卡</h1>
            <p>多语言国际化更新完成 - 将"推荐任务"改为"激活约会卡"</p>
        </div>

        <div class="test-container">
            <h2>🌍 多语言翻译对照</h2>
            <p>以下是"激活约会卡"在所有支持语言中的翻译：</p>
            
            <div class="language-grid">
                <div class="language-card">
                    <div class="language-name">中文 (zh_cn)</div>
                    <div class="translation-text">激活约会卡</div>
                </div>
                
                <div class="language-card">
                    <div class="language-name">English (en_us)</div>
                    <div class="translation-text">Activate Dating Card</div>
                </div>
                
                <div class="language-card">
                    <div class="language-name">한국어 (ko_hy)</div>
                    <div class="translation-text">데이트 카드 활성화</div>
                </div>
                
                <div class="language-card">
                    <div class="language-name">Español (es_spa)</div>
                    <div class="translation-text">Activar Tarjeta de Citas</div>
                </div>
                
                <div class="language-card">
                    <div class="language-name">Bahasa Melayu (ms_my)</div>
                    <div class="translation-text">Aktifkan Kad Temu Janji</div>
                </div>
                
                <div class="language-card">
                    <div class="language-name">Bahasa Indonesia (idn_yu)</div>
                    <div class="translation-text">Aktifkan Kartu Kencan</div>
                </div>
                
                <div class="language-card">
                    <div class="language-name">Tiếng Việt (yn_yu)</div>
                    <div class="translation-text">Kích Hoạt Thẻ Hẹn Hò</div>
                </div>
            </div>
        </div>
        
        <div class="test-container">
            <h2>📱 页面显示效果</h2>
            <p>在首页中，原来显示"推荐任务"的位置现在会显示"激活约会卡"，并且支持多语言切换。</p>
            
            <div class="highlight-box">
                <h4>🎯 更新重点</h4>
                <p>用户切换语言时，"激活约会卡"标题会自动更新为对应语言版本！</p>
            </div>
        </div>

        <div class="test-container">
            <h2>🔧 技术实现详情</h2>
            
            <h3>修改的文件：</h3>
            <ul>
                <li><code>src/assets/languages/zh_cn.json</code> - 中文翻译</li>
                <li><code>src/assets/languages/en_us.json</code> - 英文翻译</li>
                <li><code>src/assets/languages/ko_hy.json</code> - 韩语翻译</li>
                <li><code>src/assets/languages/es_spa.json</code> - 西班牙语翻译</li>
                <li><code>src/assets/languages/ms_my.json</code> - 马来语翻译</li>
                <li><code>src/assets/languages/idn_yu.json</code> - 印尼语翻译</li>
                <li><code>src/assets/languages/yn_yu.json</code> - 越南语翻译</li>
            </ul>

            <h3>使用的翻译键：</h3>
            <p><code>{{$t("index.task")}}</code></p>

            <h3>影响的页面：</h3>
            <ul>
                <li><code>src/pages/home/<USER>/code> - 首页</li>
            </ul>

            <h3>页面中的使用示例：</h3>
            <div class="api-data">
&lt;div class="hot-title-div"&gt;
    &lt;div&gt;
        &lt;span&gt;{{$t('index.task')}}&lt;/span&gt;
    &lt;/div&gt;
    &lt;div @click="gotoMenu('/Game')"&gt;
        &lt;span&gt;{{$t('index.more')}}&lt;/span&gt;
        &lt;van-icon name="arrow" color="#979799" /&gt;
    &lt;/div&gt;
&lt;/div&gt;
            </div>
        </div>

        <div class="summary">
            <h3>✅ 激活约会卡多语言更新完成总结</h3>
            <ul>
                <li>✅ 更新了7种语言的"推荐任务"翻译为"激活约会卡"</li>
                <li>✅ 保持了原有的翻译键 <code>index.task</code></li>
                <li>✅ 所有语言版本都使用了合适的本地化表达</li>
                <li>✅ 页面模板无需修改，自动支持新翻译</li>
                <li>✅ 支持语言切换时的动态更新</li>
                <li>✅ 翻译内容符合约会卡激活的语义</li>
            </ul>
            
            <p><strong>用户体验：</strong>现在用户在首页看到的不再是"推荐任务"，而是更符合业务场景的"激活约会卡"，并且在切换语言时会自动显示对应的翻译版本！</p>
        </div>
    </div>
</body>
</html>
