<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>组合选项测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .title {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 20px;
            color: #333;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin: 20px 0 10px 0;
            color: #666;
        }
        
        .basic-options {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        
        .combination-options {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }
        
        .bet-option {
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }
        
        .bet-option:hover {
            border-color: #f487e0;
            background: #f9f9f9;
        }
        
        .bet-option.active {
            border-color: #f487e0;
            background: linear-gradient(135deg, #f487e0, #988fba);
            color: white;
        }
        
        .bet-name {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 5px;
        }
        
        .bet-odds {
            font-size: 14px;
            color: #666;
        }
        
        .bet-option.active .bet-odds {
            color: #fff;
        }
        
        .selected-bets {
            margin-top: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        
        .selected-title {
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .selected-list {
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="title">彩票投注选项测试</div>
        
        <div class="section-title">基础选项</div>
        <div class="basic-options">
            <div class="bet-option" onclick="toggleBet(this, 'big')" data-type="big">
                <div class="bet-name">大</div>
                <div class="bet-odds">1.98</div>
            </div>
            <div class="bet-option" onclick="toggleBet(this, 'small')" data-type="small">
                <div class="bet-name">小</div>
                <div class="bet-odds">1.98</div>
            </div>
            <div class="bet-option" onclick="toggleBet(this, 'odd')" data-type="odd">
                <div class="bet-name">单</div>
                <div class="bet-odds">1.98</div>
            </div>
            <div class="bet-option" onclick="toggleBet(this, 'even')" data-type="even">
                <div class="bet-name">双</div>
                <div class="bet-odds">1.98</div>
            </div>
        </div>
        
        <div class="section-title">组合选项</div>
        <div class="combination-options">
            <div class="bet-option" onclick="toggleBet(this, 'big_odd')" data-type="big_odd">
                <div class="bet-name">大单</div>
                <div class="bet-odds">3.8</div>
            </div>
            <div class="bet-option" onclick="toggleBet(this, 'big_even')" data-type="big_even">
                <div class="bet-name">大双</div>
                <div class="bet-odds">3.8</div>
            </div>
            <div class="bet-option" onclick="toggleBet(this, 'small_odd')" data-type="small_odd">
                <div class="bet-name">小单</div>
                <div class="bet-odds">3.8</div>
            </div>
            <div class="bet-option" onclick="toggleBet(this, 'small_even')" data-type="small_even">
                <div class="bet-name">小双</div>
                <div class="bet-odds">3.8</div>
            </div>
        </div>
        
        <div class="selected-bets">
            <div class="selected-title">已选择的投注:</div>
            <div class="selected-list" id="selectedList">无</div>
        </div>
    </div>

    <script>
        let selectedBets = [];
        
        function toggleBet(element, type) {
            const isActive = element.classList.contains('active');
            
            if (isActive) {
                element.classList.remove('active');
                selectedBets = selectedBets.filter(bet => bet !== type);
            } else {
                element.classList.add('active');
                selectedBets.push(type);
            }
            
            updateSelectedList();
        }
        
        function updateSelectedList() {
            const listElement = document.getElementById('selectedList');
            if (selectedBets.length === 0) {
                listElement.textContent = '无';
            } else {
                const betNames = {
                    'big': '大',
                    'small': '小',
                    'odd': '单',
                    'even': '双',
                    'big_odd': '大单',
                    'big_even': '大双',
                    'small_odd': '小单',
                    'small_even': '小双'
                };
                
                const names = selectedBets.map(bet => betNames[bet]).join(', ');
                listElement.textContent = names;
            }
        }
    </script>
</body>
</html>
