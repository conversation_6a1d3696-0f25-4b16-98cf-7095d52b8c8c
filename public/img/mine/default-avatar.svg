<svg width="80" height="80" viewBox="0 0 80 80" fill="none" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <linearGradient id="avatarGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#f487e0;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#e6c3a1;stop-opacity:1" />
    </linearGradient>
  </defs>
  <circle cx="40" cy="40" r="40" fill="url(#avatarGradient)"/>
  <circle cx="40" cy="30" r="12" fill="white" opacity="0.9"/>
  <path d="M20 65 C20 55, 28 50, 40 50 C52 50, 60 55, 60 65" fill="white" opacity="0.9"/>
</svg>
