<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>公司简历多语言国际化测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f2f2f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        h1 {
            color: #333;
            text-align: center;
        }
        h2 {
            color: #666;
            border-bottom: 2px solid #f487e0;
            padding-bottom: 10px;
        }
        .highlight-box {
            background: linear-gradient(45deg, #ff6b6b, #ee5a24);
            color: white;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            border: 3px solid #ff4757;
        }
        .highlight-box h4 {
            margin: 0 0 10px 0;
            color: white;
        }
        .comparison-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        .comparison-table th,
        .comparison-table td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        .comparison-table th {
            background-color: #f487e0;
            color: white;
            font-weight: bold;
        }
        .comparison-table tr:nth-child(even) {
            background-color: #f9f9f9;
        }
        .translation-key {
            background: #007bff;
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
        }
        .language-section {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        .language-title {
            color: #495057;
            margin: 0 0 10px 0;
            font-weight: bold;
        }
        .field-list {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            font-size: 14px;
        }
        .field-item {
            background: white;
            padding: 8px;
            border-radius: 4px;
            border-left: 3px solid #f487e0;
        }
        .summary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-top: 30px;
        }
        .summary h3 {
            color: white;
            margin-top: 0;
        }
        .summary ul {
            margin: 0;
            padding-left: 20px;
        }
        .summary li {
            margin: 8px 0;
        }
        .api-data {
            background: #2d3748;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            margin: 15px 0;
            overflow-x: auto;
        }
        .data-field {
            color: #68d391;
        }
        .data-value {
            color: #fbb6ce;
        }
    </style>
</head>
<body>
    <h1>公司简历多语言国际化测试</h1>
    
    <div class="test-container">
        <h2>🌍 国际化更新说明</h2>
        <p>将公司简历页面中的所有固定文本都改为多语言翻译键，实现完整的国际化支持。</p>
        
        <div class="highlight-box">
            <h4>🎯 更新重点</h4>
            <p>除了后台返回的数据外，页面中的所有标签、按钮、提示文本都使用多语言模板！</p>
        </div>
    </div>

    <div class="test-container">
        <h2>📝 新增翻译键列表</h2>
        
        <table class="comparison-table">
            <thead>
                <tr>
                    <th>翻译键</th>
                    <th>中文</th>
                    <th>英文</th>
                    <th>用途说明</th>
                </tr>
            </thead>
            <tbody>
                <tr>
                    <td><span class="translation-key">company_profile</span></td>
                    <td>公司简介</td>
                    <td>Company Profile</td>
                    <td>页面标题和简介标题</td>
                </tr>
                <tr>
                    <td><span class="translation-key">company_details</span></td>
                    <td>公司详情</td>
                    <td>Company Details</td>
                    <td>公司详情卡片标题</td>
                </tr>
                <tr>
                    <td><span class="translation-key">contact_info</span></td>
                    <td>联系信息</td>
                    <td>Contact Information</td>
                    <td>联系信息卡片标题</td>
                </tr>
                <tr>
                    <td><span class="translation-key">company_advantages</span></td>
                    <td>企业优势</td>
                    <td>Company Advantages</td>
                    <td>企业优势卡片标题</td>
                </tr>
                <tr>
                    <td><span class="translation-key">industry</span></td>
                    <td>所属行业</td>
                    <td>Industry</td>
                    <td>行业字段标签</td>
                </tr>
                <tr>
                    <td><span class="translation-key">founded_time</span></td>
                    <td>成立时间</td>
                    <td>Founded</td>
                    <td>成立时间字段标签</td>
                </tr>
                <tr>
                    <td><span class="translation-key">employee_scale</span></td>
                    <td>员工规模</td>
                    <td>Employees</td>
                    <td>员工规模字段标签</td>
                </tr>
                <tr>
                    <td><span class="translation-key">company_address</span></td>
                    <td>公司地址</td>
                    <td>Address</td>
                    <td>地址字段标签</td>
                </tr>
                <tr>
                    <td><span class="translation-key">contact_phone</span></td>
                    <td>联系电话</td>
                    <td>Phone</td>
                    <td>电话字段标签</td>
                </tr>
                <tr>
                    <td><span class="translation-key">official_email</span></td>
                    <td>官方邮箱</td>
                    <td>Email</td>
                    <td>邮箱字段标签</td>
                </tr>
                <tr>
                    <td><span class="translation-key">official_website</span></td>
                    <td>官方网站</td>
                    <td>Website</td>
                    <td>网站字段标签</td>
                </tr>
                <tr>
                    <td><span class="translation-key">professional_service</span></td>
                    <td>专业服务，值得信赖</td>
                    <td>Professional Service, Trustworthy</td>
                    <td>公司口号</td>
                </tr>
                <tr>
                    <td><span class="translation-key">service_24_7</span></td>
                    <td>7×24小时服务</td>
                    <td>24/7 Service</td>
                    <td>企业优势项</td>
                </tr>
                <tr>
                    <td><span class="translation-key">professional_team</span></td>
                    <td>专业技术团队</td>
                    <td>Professional Team</td>
                    <td>企业优势项</td>
                </tr>
                <tr>
                    <td><span class="translation-key">secure_platform</span></td>
                    <td>安全可靠平台</td>
                    <td>Secure Platform</td>
                    <td>企业优势项</td>
                </tr>
                <tr>
                    <td><span class="translation-key">quality_experience</span></td>
                    <td>优质用户体验</td>
                    <td>Quality Experience</td>
                    <td>企业优势项</td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="test-container">
        <h2>🌐 各语言翻译展示</h2>
        
        <div class="language-section">
            <h3 class="language-title">🇨🇳 中文 (zh_cn)</h3>
            <div class="field-list">
                <div class="field-item">公司简介</div>
                <div class="field-item">公司详情</div>
                <div class="field-item">联系信息</div>
                <div class="field-item">企业优势</div>
                <div class="field-item">所属行业</div>
                <div class="field-item">成立时间</div>
                <div class="field-item">员工规模</div>
                <div class="field-item">公司地址</div>
            </div>
        </div>

        <div class="language-section">
            <h3 class="language-title">🇺🇸 英文 (en_us)</h3>
            <div class="field-list">
                <div class="field-item">Company Profile</div>
                <div class="field-item">Company Details</div>
                <div class="field-item">Contact Information</div>
                <div class="field-item">Company Advantages</div>
                <div class="field-item">Industry</div>
                <div class="field-item">Founded</div>
                <div class="field-item">Employees</div>
                <div class="field-item">Address</div>
            </div>
        </div>

        <div class="language-section">
            <h3 class="language-title">🇮🇩 印尼语 (idn_yu)</h3>
            <div class="field-list">
                <div class="field-item">Profil Perusahaan</div>
                <div class="field-item">Detail Perusahaan</div>
                <div class="field-item">Informasi Kontak</div>
                <div class="field-item">Keunggulan Perusahaan</div>
                <div class="field-item">Industri</div>
                <div class="field-item">Didirikan</div>
                <div class="field-item">Karyawan</div>
                <div class="field-item">Alamat</div>
            </div>
        </div>

        <div class="language-section">
            <h3 class="language-title">🇰🇷 韩语 (ko_hy)</h3>
            <div class="field-list">
                <div class="field-item">회사 프로필</div>
                <div class="field-item">회사 세부정보</div>
                <div class="field-item">연락처 정보</div>
                <div class="field-item">회사 장점</div>
                <div class="field-item">업종</div>
                <div class="field-item">설립일</div>
                <div class="field-item">직원 수</div>
                <div class="field-item">회사 주소</div>
            </div>
        </div>
    </div>

    <div class="test-container">
        <h2>📊 API数据映射</h2>
        
        <h3>后台返回的数据字段：</h3>
        <div class="api-data">
{
  "code": 200,
  "msg": "获取信息成功",
  "data": {
    "<span class="data-field">company_name</span>": "<span class="data-value">示例科技有限公司</span>",
    "<span class="data-field">company_description</span>": "<span class="data-value">专注于互联网技术开发</span>",
    "<span class="data-field">company_industry</span>": "<span class="data-value">互联网</span>",
    "<span class="data-field">company_founded</span>": "<span class="data-value">2020年</span>",
    "<span class="data-field">company_employees</span>": "<span class="data-value">100-500人</span>",
    "<span class="data-field">company_address</span>": "<span class="data-value">北京市朝阳区xxx街道</span>",
    "<span class="data-field">company_phone</span>": "<span class="data-value">010-12345678</span>",
    "<span class="data-field">company_email</span>": "<span class="data-value"><EMAIL></span>",
    "<span class="data-field">company_website</span>": "<span class="data-value">https://www.example.com</span>",
    "<span class="data-field">company_logo</span>": "<span class="data-value">http://your-domain/uploads/company/logo/logo.png</span>"
  }
}
        </div>

        <h3>数据展示逻辑：</h3>
        <ul>
            <li>✅ <strong>后台数据</strong>：直接显示API返回的内容</li>
            <li>✅ <strong>标签文本</strong>：使用多语言翻译键 <code>{{ $t('my.field_name') }}</code></li>
            <li>✅ <strong>默认值</strong>：数据缺失时显示翻译后的默认文本</li>
            <li>✅ <strong>动态切换</strong>：用户切换语言时，标签自动更新</li>
        </ul>
    </div>

    <div class="test-container">
        <h2>🔧 技术实现详情</h2>
        
        <h3>修改的文件：</h3>
        <ul>
            <li><code>src/pages/mine/Personalreport.vue</code> - 页面模板更新</li>
            <li><code>src/assets/languages/zh_cn.json</code> - 中文翻译</li>
            <li><code>src/assets/languages/en_us.json</code> - 英文翻译</li>
            <li><code>src/assets/languages/idn_yu.json</code> - 印尼语翻译</li>
            <li><code>src/assets/languages/ko_hy.json</code> - 韩语翻译</li>
            <li><code>src/assets/languages/ms_my.json</code> - 马来语翻译</li>
            <li><code>src/assets/languages/yn_yu.json</code> - 越南语翻译</li>
            <li><code>src/assets/languages/es_spa.json</code> - 西班牙语翻译</li>
        </ul>

        <h3>使用示例：</h3>
        <div class="api-data">
&lt;!-- 页面标题 --&gt;
&lt;h3 class="card-title"&gt;
  &lt;van-icon name="info-o" /&gt;
  {{ $t('my.company_profile') }}
&lt;/h3&gt;

&lt;!-- 字段标签 --&gt;
&lt;span class="detail-label"&gt;{{ $t('my.industry') }}&lt;/span&gt;
&lt;span class="detail-value"&gt;{{ companyInfo.company_industry || $t('my.industry') }}&lt;/span&gt;

&lt;!-- 企业优势 --&gt;
&lt;span&gt;{{ $t('my.service_24_7') }}&lt;/span&gt;
        </div>
    </div>

    <div class="summary">
        <h3>✅ 国际化更新完成总结</h3>
        <ul>
            <li>✅ 新增了16个翻译键，覆盖所有页面文本</li>
            <li>✅ 更新了7种语言的翻译文件</li>
            <li>✅ 页面模板完全使用多语言翻译键</li>
            <li>✅ 保持了API数据的原始显示</li>
            <li>✅ 实现了标签和内容的分离</li>
            <li>✅ 支持语言切换时的动态更新</li>
        </ul>
        
        <p><strong>用户体验：</strong>现在用户切换语言时，公司简历页面的所有标签和固定文本都会自动更新为对应语言，而后台返回的公司数据保持原样显示！</p>
    </div>
</body>
</html>
