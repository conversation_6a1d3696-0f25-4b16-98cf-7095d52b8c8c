import axios from 'axios'
import api from './api'
import Vue from 'vue';
let Base64 = require('js-base64').Base64;
import { Toast } from 'vant';
import qs from "qs";
import { mapSpecificApiResponse } from '../utils/koreanMapping';

Vue.use(Toast);
//创建axios实例对象
const  instance = axios.create({
    // baseURL: 'https://adminmanage.l-o-v-e.homes/api', //服务器地址
    baseURL: 'https://jsdao.cc/api', //服务器地址
    timeout: 5000, //默认超时时长
})

//请求拦截器
instance.interceptors.request.use(config=>{
    // 检查是否是EPUSDT充值相关接口
    const isEpusdtApi = config.url && (
        config.url.includes('/recharge/createOrder') ||
        config.url.includes('/recharge/getOrderStatus') ||
        config.url.includes('/recharge/getRechargeConfig')
    );

    // 初始化headers
    config.headers = config.headers || {};

    if(config.method === "post"){
        if(isEpusdtApi) {
            // EPUSDT接口使用JSON格式
            config.headers['Content-Type'] = 'application/json';
        } else {
            // 其他接口使用表单格式
            config.headers['content-type'] = 'application/x-www-form-urlencoded;charset=UTF-8';
        }
    }

    if(localStorage.getItem('token')){
        const userToken = localStorage.getItem('token');
        const encodedToken = Base64.encode(userToken);

        // 调试信息
        if(isEpusdtApi) {
            console.log('EPUSDT API - 原始token:', userToken);
            console.log('EPUSDT API - Base64编码token:', encodedToken);
        }

        config.headers['token'] = encodedToken;
    } else {
        console.warn('未找到token，用户可能未登录');
    }
    config.headers['lang'] = 'zh_cn';
    var yvyan=localStorage.getItem('lang')
    if(yvyan){
        config.headers['lang2'] = yvyan;
    }else{
        config.headers['lang2'] = 'zh_cn';
    }

    // 调试：显示完整的请求配置
    if(isEpusdtApi) {
        console.log('EPUSDT API - 完整请求配置:', {
            url: config.url,
            method: config.method,
            headers: config.headers,
            data: config.data
        });
    }

    return config
},err=>{
    console.error('请求失败',err)
})

//响应拦截器
instance.interceptors.response.use(res=>{
    //此处对响应数据做处理
    if(res.data.msg === "鉴权错误"){
        localStorage.clear();
        // 注意：这里不能使用this.$router，因为拦截器中没有Vue实例上下文
        window.location.href = '/Auth';
    }
    return res //该返回对象会传到请求方法的响应对象中
},err=>{
    // 响应错误处理
    console.error('响应拦截器捕获错误:', err);
    // 如果后端返回了错误信息，保持原有格式
    if (err.response && err.response.data) {
        return Promise.reject(err.response.data);
    }
    // 否则返回统一格式的错误
    return Promise.reject({
        code: err.response?.status || 500,
        msg: err.message || '网络请求失败',
        data: null
    });
})

//封装axios请求方法，参数为配置对象
//option = {method,url,params} method为请求方法，url为请求接口，params为请求参数
async function http(option = {}) {
    let result = null
    // 获取当前语言设置
    const currentLang = localStorage.getItem('lang') || 'zh_cn';

    if(option.method === 'get' || option.method === 'delete'){
        //处理get、delete请求
        await instance[option.method](
            api[option.url],
            {params: option.data}
        ).then(res=>{
            result = res.data
            try {
                result.data = JSON.parse(Base64.decode(result.data));
                // 自动映射韩文内容 - 使用特定API映射
                result.data = mapSpecificApiResponse(api[option.url], result.data, currentLang);
            } catch (decodeError) {
                console.error('Base64解码失败:', decodeError);
                // 如果解码失败，保持原始数据
                console.log('原始响应数据:', result);
            }
        }).catch(err=>{
            console.error('GET/DELETE请求失败:', err);
            // 统一错误格式
            result = {
                code: err.response?.status || 500,
                msg: err.response?.data?.msg || err.message || '网络请求失败',
                data: null
            }
        })
    }else if(option.method === 'post' || option.method === 'put'){
        //处理post、put请求
        // 检查是否是EPUSDT充值相关接口
        const isEpusdtApi = api[option.url] && (
            api[option.url].includes('/recharge/createOrder') ||
            api[option.url].includes('/recharge/getOrderStatus') ||
            api[option.url].includes('/recharge/getRechargeConfig')
        );

        // 根据接口类型选择数据格式
        const requestData = isEpusdtApi ? option.data : qs.stringify(option.data);

        await instance[option.method](
                api[option.url],
            requestData
        ).then(res=>{
            result = res.data;

            // EPUSDT接口不需要Base64解码
            if(isEpusdtApi) {
                // 直接使用响应数据，不进行Base64解码
                console.log('EPUSDT API响应:', result);
            } else {
                // 其他接口需要Base64解码
                try {
                    result.data = JSON.parse(Base64.decode(result.data));
                    // 自动映射韩文内容 - 使用特定API映射
                    result.data = mapSpecificApiResponse(api[option.url], result.data, currentLang);
                } catch (decodeError) {
                    console.error('Base64解码失败:', decodeError);
                    // 如果解码失败，保持原始数据
                    console.log('原始响应数据:', result);
                }
            }
        }).catch(err=>{
            console.error('POST/PUT请求失败:', err);
            // 统一错误格式
            result = {
                code: err.response?.status || 500,
                msg: err.response?.data?.msg || err.message || '网络请求失败',
                data: null
            }
        })
    }
    return result
}

export default http

