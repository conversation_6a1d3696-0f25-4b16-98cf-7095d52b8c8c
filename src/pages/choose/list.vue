<template>
	<div class="container page">
		<van-nav-bar :title="this.vod_name" class="nav-bar">
			<template #left>
				<van-icon name="arrow-left" color="#fff" @click="back()" />
			</template>
		</van-nav-bar>

		<!-- 性别选择标签页 -->
		<van-tabs v-model="activeTab" animated sticky line-width="100px" :swipeable="true" @change="onTabChange">
			<van-tab :title="$t('concubine.female_section')" name="female">
				<div class="right">
					<van-pull-refresh
					:pulling-text="$t('reservation.pull_refresh_pulling')"
					:loosing-text="$t('reservation.pull_refresh_loosing')"
					:loading-text="$t('reservation.pull_refresh_loading')"
					:success-text="$t('reservation.pull_refresh_success')" border="false" class="list-wrapper" v-model="isLoading" @refresh="onRefresh">
						<van-grid :column-num="2" :gutter="10">
							<van-grid-item @click="profile(v.id, 'female')" v-for="(v, k) in femaleDatalist" :key="'female-' + k">
								<van-image class="game_item_img" :src="common.getImageUrl(v.img_url)">
									<template v-slot:loading>
										<van-loading type="circular" />
									</template>
								</van-image>
								<span class="rig-name">{{ v.xuanfei_name }}</span>
							</van-grid-item>
						</van-grid>
					</van-pull-refresh>
				</div>
			</van-tab>

			<van-tab :title="$t('concubine.male_section')" name="male">
				<div class="right">
					<van-pull-refresh
					:pulling-text="$t('reservation.pull_refresh_pulling')"
					:loosing-text="$t('reservation.pull_refresh_loosing')"
					:loading-text="$t('reservation.pull_refresh_loading')"
					:success-text="$t('reservation.pull_refresh_success')" border="false" class="list-wrapper" v-model="isLoading" @refresh="onRefresh">
						<van-grid :column-num="2" :gutter="10">
							<van-grid-item @click="profile(v.id, 'male')" v-for="(v, k) in maleDatalist" :key="'male-' + k">
								<van-image class="game_item_img" :src="common.getImageUrl(v.img_url)">
									<template v-slot:loading>
										<van-loading type="circular" />
									</template>
								</van-image>
								<span class="rig-name">{{ v.xuanfei_name }}</span>
							</van-grid-item>
						</van-grid>
					</van-pull-refresh>
				</div>
			</van-tab>
		</van-tabs>
	</div>
</template>

<script>
export default {
	data() {
		return {
			vod_name: '北京',
			isLoading: false,
			activeTab: 'female', // 默认显示女生板块
			femaleDatalist: [], // 女生数据
			maleDatalist: [], // 男生数据
			datalist: [] // 保留原有数据结构以兼容
		};
	},
	methods: {
		back() {
			this.$router.push({ path: 'Choose' });
		},
		onRefresh() {
			setTimeout(() => {
				this.$toast(this.$t("reservation.refresh"));
				this.isLoading = false;
				// 重新加载当前标签页的数据
				if (this.activeTab === 'female') {
					this.getxuanfeilist();
				} else {
					this.getMaleList();
				}
			}, 500);
		},
		onTabChange(name) {
			// 切换标签页时加载对应数据
			if (name === 'male' && this.maleDatalist.length === 0) {
				this.getMaleList();
			}
		},
		profile(id, gender) {
			// 根据性别传递不同的参数
			this.$router.push({
				path: '/profile?id=' + id + '&name=' + this.vod_name + '&adsid=' + this.$route.query.id + '&gender=' + gender
			});
		},
		// 获取女生列表（原有接口）
		getxuanfeilist() {
			this.$http({
				method: 'get',
				url: 'xuanfeilist',
				data: { id: this.$route.query.id }
			}).then(res => {
				this.femaleDatalist = res.data;
				this.datalist = res.data; // 保持兼容性
			}).catch(err => {
				console.error('获取女生列表失败:', err);
				this.$toast.fail('获取女生列表失败');
			});
		},
		// 获取男生列表（新接口）
		getMaleList() {
			this.$http({
				method: 'get',
				url: 'malelist',
				data: { id: this.$route.query.id },
				headers: {
					'lang': 'zh'
				}
			}).then(res => {
				this.maleDatalist = res.data;
				console.log('男生列表数据:', res.data);
			}).catch(err => {
				console.error('获取男生列表失败:', err);
				this.$toast.fail('获取男生列表失败');
			});
		}
	},
	created() {
		this.vod_name = this.$route.query.name;
		// 默认加载女生列表
		this.getxuanfeilist();
	}
};
</script>

<style scoped>
.right {
	margin-top: 10px;
}

/* 标签页样式 */
::v-deep .van-tab {
	font-size: 30px;
	line-height: 100px;
	font-weight: bold;
}
::v-deep .van-tabs__line {
	background-color: #f487e0;
}
::v-deep .van-tabs--line .van-tabs__wrap {
	height: 100px;
}
::v-deep .van-tabs__wrap--scrollable .van-tab {
	padding: 0 23px;
}

::v-deep .van-grid-item__content--center {
	border-radius: 15px;
	padding: 0;
	height: auto;
}
::v-deep .van-image__img {
	border-radius: 10px;
	padding: 15px;
}
.rig-name {
	width: 100%;
	height: 60px;
	line-height: 60px;
	margin-top: 10px;
	background-color: #f7f7f7;
	border-radius: 0 0 15px 15px;
	font-size: 15px;
	padding-left: 10px;
}
</style>
