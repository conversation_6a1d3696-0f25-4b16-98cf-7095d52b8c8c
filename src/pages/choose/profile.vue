<template>
	<div class="page">
		<van-nav-bar :title="$t('concubine.concubine')" class="nav-bar">
			<template #left>
				<van-icon name="arrow-left" color="#fff" @click="back()" />
			</template>
		</van-nav-bar>
		<div class="box">
			<p class="name">{{ this.xuanfeidata.xuanfei_name }}</p>
			<p class="title">{{ this.xuanfeidata.vo_title }}</p>

			<!-- 显示图片 -->
			<div v-if="gender === 'female'">
				<!-- 女生图片 -->
				<van-image
					width="98%"
					fit="contain"
					height="100%"
					v-for="(v, k) in xuanfeidata.img_url"
					:key="'female-img-' + k"
					:src="common.getImageUrl(v)"
				/>
			</div>
			<div v-else>
				<!-- 男生图片 -->
				<van-image
					width="98%"
					fit="contain"
					height="100%"
					v-for="(v, k) in maleImages"
					:key="'male-img-' + k"
					:src="common.getImageUrl(v)"
				/>
			</div>

			<!-- 显示视频 -->
			<div v-if="xuanfeidata.video_url" class="video-container">
				<video
					ref="videoPlayer"
					:src="common.getVideoUrl(xuanfeidata.video_url)"
					:poster="common.getImageUrl(xuanfeidata.video_preview)"
					controls
					preload="metadata"
					class="video-player"
					playsinline
					webkit-playsinline
					x5-video-player-type="h5"
					x5-video-player-fullscreen="true"
					@loadedmetadata="onVideoLoaded"
					@play="onVideoPlay"
					@timeupdate="onTimeUpdate"
					@error="onVideoError"
				>
					<source :src="common.getVideoUrl(xuanfeidata.video_url)" :type="common.getVideoType(xuanfeidata.video_url)">
					您的浏览器不支持视频播放
				</video>
				<div class="video-info" v-if="xuanfeidata.video_duration">
					<span class="video-duration">{{ formatDuration(xuanfeidata.video_duration) }}</span>
					<span class="video-size" v-if="xuanfeidata.video_size">{{ formatFileSize(xuanfeidata.video_size) }}</span>
				</div>
			</div>

			<van-button round="true" @click="yuyue" class="button" color="linear-gradient(to right, #7f5778 , #e5c2a0)">{{$t("foorter.subscribe")}}</van-button>
		</div>
	</div>
</template>

<script>
export default {
	data() {
		return {
			xuanfeidata: [],
			userInfo: {},
			is_playing: false,
			watch_timer: null,
			gender: 'female', // 性别标识
			maleImages: [] // 男生素材图片
		};
	},
	methods: {
		back() {
			this.$router.push({ path: 'list?id=' + this.$route.query.adsid + '&name=' + this.$route.query.name });
		},
		getxuanfeidata() {
			// 根据性别调用不同的接口
			if (this.gender === 'male') {
				// 男生数据：先获取基本信息，再获取素材图片
				this.$http({
					method: 'get',
					url: 'xuanfeidata',
					data: { id: this.$route.query.id }
				}).then(res => {
					this.xuanfeidata = res.data;
					console.log('男生详情数据:', res.data);
					// 获取男生素材图片
					this.getMaleImages();
				}).catch(err => {
					console.error('获取男生详情失败:', err);
				});
			} else {
				// 女生数据：使用原有接口
				this.$http({
					method: 'get',
					url: 'xuanfeidata',
					data: { id: this.$route.query.id }
				}).then(res => {
					this.xuanfeidata = res.data;
					console.log('女生详情数据:', res.data);
				}).catch(err => {
					console.error('获取女生详情失败:', err);
				});
			}
		},
		// 获取男生素材图片
		getMaleImages() {
			this.$http({
				method: 'post',
				url: 'maleImages',
				data: { id: this.$route.query.id }
			}).then(res => {
				this.maleImages = res.data;
				console.log('男生素材图片:', res.data);
			}).catch(err => {
				console.error('获取男生素材图片失败:', err);
			});
		},
		yuyue() {
			this.$toast(this.$t("reservation.counselor"));
		},
		// 获取用户信息检查会员状态
		getUserInfo() {
			this.$http({
				method: 'get',
				url: 'user_info'
			}).then(res => {
				if (res.code === 200) {
					this.userInfo = res.data;
					console.log('选妃页面用户信息:', this.userInfo);
					console.log('VIP状态:', this.userInfo.vip, '剩余天数:', this.userInfo.vip_remaining_days);
				}
			}).catch(err => {
				console.log('获取用户信息失败:', err);
			});
		},
		// 判断是否为VIP会员
		isVipMember(userInfo) {
			// 如果有VIP等级且剩余天数大于0，则为会员
			if (userInfo.vip && userInfo.vip_remaining_days > 0) {
				return true; // 会员
			}
			// 如果原来的is_see字段存在，也使用它作为备用判断
			if (userInfo.is_see === 1) {
				return true; // 会员
			}
			return false; // 非会员
		},
		// 视频开始播放事件
		onVideoPlay() {
			this.is_playing = true;
			// 如果用户不是会员，开始计时
			if (!this.isVipMember(this.userInfo)) {
				this.startWatchTimer();
			}
		},
		// 视频时间更新事件
		onTimeUpdate(event) {
			if (this.is_playing && !this.isVipMember(this.userInfo)) {
				const currentTime = Math.round(event.target.currentTime);
				if (currentTime >= 8) {
					this.pauseVideoForNonMember();
				}
			}
		},
		// 开始观看计时器
		startWatchTimer() {
			if (this.watch_timer) {
				clearInterval(this.watch_timer);
			}
			this.watch_timer = setInterval(() => {
				if (this.is_playing && !this.isVipMember(this.userInfo)) {
					const video = this.$refs.videoPlayer;
					if (video && video.currentTime >= 8) {
						this.pauseVideoForNonMember();
					}
				}
			}, 1000);
		},
		// 暂停视频并提示非会员限制
		pauseVideoForNonMember() {
			const video = this.$refs.videoPlayer;
			if (video) {
				video.pause();
				this.is_playing = false;
			}
			if (this.watch_timer) {
				clearInterval(this.watch_timer);
				this.watch_timer = null;
			}
			this.$toast(this.$t("video.member_limit"));
		},
		// 格式化视频时长（秒转换为分:秒格式）
		formatDuration(seconds) {
			if (!seconds) return '';
			const minutes = Math.floor(seconds / 60);
			const remainingSeconds = seconds % 60;
			return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
		},
		// 格式化文件大小
		formatFileSize(bytes) {
			if (!bytes) return '';
			const sizes = ['B', 'KB', 'MB', 'GB'];
			const i = Math.floor(Math.log(bytes) / Math.log(1024));
			return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
		},
		// 视频加载完成事件
		onVideoLoaded(event) {
			console.log('视频加载完成:', event.target.duration);
		},
		// 视频错误处理
		onVideoError(event) {
			console.error('视频播放错误:', event);
			const video = event.target;
			const error = video.error;

			if (error) {
				console.error('错误代码:', error.code);
				console.error('错误信息:', error.message);

				// 根据错误类型提供不同的处理方案
				switch (error.code) {
					case 1: // MEDIA_ERR_ABORTED
						console.log('视频播放被中止');
						break;
					case 2: // MEDIA_ERR_NETWORK
						console.log('网络错误，无法加载视频');
						this.$toast('网络错误，请检查网络连接');
						break;
					case 3: // MEDIA_ERR_DECODE
						console.log('视频解码错误');
						this.$toast('视频格式不支持，请联系客服');
						break;
					case 4: // MEDIA_ERR_SRC_NOT_SUPPORTED
						console.log('视频源不支持');
						this.$toast('视频格式不支持或文件损坏');
						break;
					default:
						console.log('未知视频错误');
						this.$toast('视频播放失败，请稍后重试');
						break;
				}
			}
		}
	},
	created() {
		// 获取性别参数
		this.gender = this.$route.query.gender || 'female';
		console.log('当前性别:', this.gender);

		this.getxuanfeidata();
		// 如果用户已登录，获取用户信息
		if (localStorage.getItem('token')) {
			this.getUserInfo();
		}
	},
	// 组件销毁时清理计时器
	beforeDestroy() {
		if (this.watch_timer) {
			clearInterval(this.watch_timer);
			this.watch_timer = null;
		}
	}
};
</script>

<style>
.container {
	display: inline-block;
}
.box {
	width: 95%;
	margin: 0 auto;
	text-align: center;
	padding-bottom: 6.25rem;
}
.name {
	font-size: 1.125rem;
}
.title {
	font-size: 0.625rem;
}
.button {
	width: 10rem;
	height: 2.5rem;
	font-size: 0.9375rem;
	margin-top: 0.625rem;
}

/* 视频相关样式 */
.video-container {
	width: 98%;
	margin: 10px auto;
	position: relative;
}

.video-player {
	width: 100%;
	max-height: 400px;
	border-radius: 10px;
	background-color: #000;
}

.video-info {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 8px 12px;
	background-color: rgba(0, 0, 0, 0.7);
	color: white;
	font-size: 12px;
	border-radius: 0 0 10px 10px;
	margin-top: -4px;
}

.video-duration {
	background-color: rgba(255, 255, 255, 0.2);
	padding: 2px 6px;
	border-radius: 4px;
}

.video-size {
	color: #ccc;
}
</style>
