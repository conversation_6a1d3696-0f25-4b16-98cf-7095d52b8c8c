<template>
<div class="reservation-hall page">
  <van-nav-bar
      class="nav-bar"
      :title="$t('reservation.hall')"
  />
  <div class="content-wrapper">
    <van-pull-refresh
      :pulling-text="$t('reservation.pull_refresh_pulling')"
      :loosing-text="$t('reservation.pull_refresh_loosing')"
      :loading-text="$t('reservation.pull_refresh_loading')"
      :success-text="$t('reservation.pull_refresh_success')"
      :border="false"
      v-model="isLoading"
      @refresh="onRefresh">

      <!-- 游戏类型卡片 -->
      <div class="game-cards-section">
        <div class="section-header">
          <h2 class="section-title">🎮 打賞主播解鎖一起玩</h2>
        </div>
        <div class="game-cards-grid">
          <div class="game-card" @click="toCustomerService()">
            <div class="game-card-bg" :style="{ backgroundColor: '#FF9A9E' }">
              <div class="game-card-avatar">
                <img src="img/lottery/Group1.1d90b9cf.png" alt="主播" />
              </div>
              <div class="game-card-content">
                <div class="game-card-title">主播在線陪玩</div>
                <div class="game-card-subtitle">532人在線</div>
              </div>
            </div>
          </div>

          <div class="game-card" @click="toCustomerService()">
            <div class="game-card-bg" :style="{ backgroundColor: '#FECA57' }">
              <div class="game-card-avatar">
                <img src="img/lottery/Group2.97e0b0ae.png" alt="主播" />
              </div>
              <div class="game-card-content">
                <div class="game-card-title">線下見面會</div>
                <div class="game-card-subtitle">108人已報名</div>
              </div>
            </div>
          </div>

          <div class="game-card" @click="toCustomerService()">
            <div class="game-card-bg" :style="{ backgroundColor: '#FF6B9D' }">
              <div class="game-card-avatar">
                <img src="img/lottery/Group3.5e1245a2.png" alt="主播" />
              </div>
              <div class="game-card-content">
                <div class="game-card-title">感謝人生陪伴</div>
                <div class="game-card-subtitle">720人已參與</div>
              </div>
            </div>
          </div>

          <div class="game-card" @click="toCustomerService()">
            <div class="game-card-bg" :style="{ backgroundColor: '#A8E6CF' }">
              <div class="game-card-avatar">
                <img src="img/lottery/Group4.3de52b54.png" alt="主播" />
              </div>
              <div class="game-card-content">
                <div class="game-card-title">線下歌聲對唱</div>
                <div class="game-card-subtitle">172人已報名</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 官方直播间 -->
      <div class="live-section">
        <div class="section-header">
          <h2 class="section-title">SWAG直播打赏房间</h2>
        </div>
        <div class="live-grid">
          <div class="live-item" v-for="(v,key) in gameitem.slice(0,4)" :key="key" @click="toLottery(v.key,v.id)">
            <van-image class="live-item-bg" :src="common.getImageUrl(v.ico)">
              <template v-slot:loading>
                <van-loading type="circular"/>
              </template>
              <template v-slot:error>
                <van-icon name="photo-fail" size="32" />
              </template>
            </van-image>
            <div class="live-enter-overlay">
              <div class="live-enter-btn">
                <span class="btn-icon">⏸️</span>
                <span class="btn-text">點擊進入直播間</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </van-pull-refresh>
  </div>
</div>
</template>

<script>
import { Toast } from 'vant';
export default {
  data() {
    return {
      gameitem: [],
      isLoading: false,
      baseInfo: {},
    };
  },
  methods: {
    onRefresh() {
      setTimeout(() => {
        Toast(this.$t("reservation.refresh"));
        this.isLoading = false;
      }, 500);
    },
    toLottery(key,id){
      if(!localStorage.getItem('token')){
        this.$router.push({path:'/Register'})
      }else {
        this.$router.push({path:'/Lottery?key='+key+"&id="+id})
      }
    },
    toLiveRoom(room) {
      // 跳转到直播间
      console.log('进入直播间:', room);
      // 这里可以添加具体的直播间跳转逻辑
      // 例如：window.open(room.liveUrl) 或者路由跳转
    },
    getGameItem(){
      this.$http({
        method: 'get',
        url: 'lottery_list'
      }).then(res=>{
        console.log('API 响应:', res);
        if (res && res.data) {
          this.gameitem = res.data;
        } else {
          console.error('API 返回数据格式异常:', res);
        }
      }).catch(err => {
        console.error('API 请求失败:', err);
      })
    },
    getBaseInfo(){
      this.$http({
        method: 'get',
        url: 'base_info'
      }).then(res=>{
        if (res && res.data) {
          this.baseInfo = res.data;
          this.$store.commit('setBaseInfoValue', res.data);
        }
      }).catch(err => {
        console.error('获取基础信息失败:', err);
      })
    },
    toCustomerService() {
      // 检查是否已登录
      if(!localStorage.getItem('token')){
        this.$router.push({path:'/Register'})
        return;
      }

      // 获取客服链接并跳转
      const baseInfo = this.$store.getters.getBaseInfo;
      if (baseInfo && baseInfo.kefu && baseInfo.iskefu == 1) {
        window.location.href = baseInfo.kefu;
      } else {
        this.$toast.fail('客服暂时不可用');
      }
    },

  },
  created() {
    this.getGameItem();//获取首页游戏列表
    this.getBaseInfo();//获取基础信息
  }
};
</script>

<style lang='less' scoped>
.page{
  position: absolute!important;
  top: 0;
  left: 0;
  right: 0;
  bottom: 110px; /* 为底部导航栏预留空间 */
  background-color: #f2f2f5;
}

.nav-bar{
  background: linear-gradient(135deg, #c084fc, #a855f7);
  height: 100px;
}

.van-nav-bar {
  line-height: 50px;
}

::v-deep .van-nav-bar__title {
  max-width: 60%;
  margin: 0 auto;
  color: #ffffff;
  font-size: 35px;
  font-weight: 600;
}

::v-deep .van-nav-bar__content {
  height: 100px;
}

.reservation-hall{
  display: flex;
  flex-direction: column;
  height: 100%;
  background: #f2f2f5;
}

.content-wrapper {
  flex: 1;
  padding: 20px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}

/* 游戏卡片样式 */
.game-cards-section {
  margin-bottom: 30px;
}

.game-cards-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 15px;
  padding: 0 10px;
}

.game-card {
  cursor: pointer;
  transition: all 0.3s ease;
}

.game-card:active {
  transform: scale(0.95);
}

.game-card-bg {
  position: relative;
  height: 120px;
  border-radius: 15px;
  padding: 15px;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, var(--bg-color), var(--bg-color-light));
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.game-card-bg::before {
  content: '';
  position: absolute;
  top: -50%;
  right: -20px;
  width: 100px;
  height: 100px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  transform: rotate(45deg);
}

.game-card-avatar {
  width: 50px;
  height: 50px;
  margin-right: 12px;
  flex-shrink: 0;
}

.game-card-avatar img {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  object-fit: cover;
  border: 2px solid rgba(255, 255, 255, 0.3);
}

.game-card-content {
  flex: 1;
  color: white;
}

.game-card-title {
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 4px;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.game-card-subtitle {
  font-size: 12px;
  opacity: 0.9;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}



/* 官方直播间样式 */
.live-section {
  padding: 0 20px 20px 20px;
}

.section-header {
  margin-bottom: 20px;
}

.section-title {
  font-size: 36px;
  font-weight: 600;
  color: #333;
  margin: 0;
  text-align: center;
}

.live-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
  height: calc(100vh - 450px); /* 减去导航栏、标题、游戏卡片和底部导航的高度 */
  overflow-y: auto; /* 允许滚动 */
  padding-bottom: 10px; /* 添加底部内边距 */
}

.live-item {
  position: relative;
  flex: 1;
  min-height: 140px; /* 增加最小高度 */
  border-radius: 15px;
  overflow: hidden;
  background: #fff;
  box-shadow: 0 2px 15px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.live-item:active {
  transform: scale(0.98);
}

.live-item-bg {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.live-enter-overlay {
  position: absolute;
  bottom: 12px;
  left: 12px;
  z-index: 2;
}

.live-enter-btn {
  display: flex;
  align-items: center;
  background: rgba(0, 0, 0, 0.7);
  padding: 6px 12px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.btn-icon {
  font-size: 14px;
  margin-right: 4px;
  color: white;
}

.btn-text {
  color: white;
  font-size: 12px;
  font-weight: 500;
}



::v-deep .van-pull-refresh__track .van-pull-refresh__head *{
  color: #666;
  font-size: 28px;
}

.loading-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 50px;
  color: #666;
  font-size: 28px;
}

.loading-placeholder span {
  margin-top: 20px;
}

</style>
