<template>
	<div class="home-container">
		<div class="linear-bg"></div>
		<div class="home-scroll">
			<div class="banner">
				<swiper class="banner_swiper" :options="bannerSwiperOption">
					<swiper-slide v-for="(v,key) in banners" :key="key">
						<van-image class="banner_img" round :src="common.getImageUrl(v.url)">
							<template v-slot:loading>
								<van-loading type="circular" />
							</template>
						</van-image>
					</swiper-slide>
				</swiper>
			</div>
			<div class="notice-bar">
				<div class="notice-content">
					<div class="notice-header">
						<van-icon name="bullhorn-o" class="notice-icon" />
						<span class="notice-title">HK SWAG</span>
						<span class="notice-subtitle">HK • RightTime - SWAG</span>
					</div>
					<div class="notice-text">
						{{ this.notice }}
					</div>
				</div>
				<div class="linear-gradient"></div>
			</div>

			<van-pull-refresh :pulling-text="$t('reservation.pull_refresh_pulling')"
			:loosing-text="$t('reservation.pull_refresh_loosing')"
			:loading-text="$t('reservation.pull_refresh_loading')"
			:success-text="$t('reservation.pull_refresh_success')" v-model="isLoading" @refresh="onRefresh">
				<div class="hot-recommend">
					<div class="movie_list_0">
						<swiper class="movie_swiper" :options="movielistSwiperOption">
							<swiper-slide v-for="(v,key) in movielist_0" :key="key">
								<van-image class="movie_cover" @click="toPlayVideo(v.id)" round :src="common.getImageUrl(v.cover)">
									<template v-slot:loading>
										<van-loading type="circular" />
									</template>
								</van-image>
								<img class="hot" v-if="key === 0" src="/img/home/<USER>">
								<img class="hot" v-if="key === 1" src="/img/home/<USER>">
								<img class="hot" v-if="key === 2" src="/img/home/<USER>">
								<div class="movie-list-item-bottom">
									<div class="movie-time-div">
										<span>{{v.title}}</span>
										<div class="van-count-down">{{v.time}}</div>
									</div>
								</div>
							</swiper-slide>
						</swiper>
					</div>
					<div class="hot-title-div">
						<div>
							<span>{{$t('index.hot')}}</span>
						</div>
						<div @click="gotoMenu('/Choose')">
							<span>{{$t('index.more')}}</span>
							<van-icon name="arrow" size="25" color="#979799" />
						</div>
					</div>
					<div class="movie_list_1">
						<div class="movie-list-item" v-for="(v,key) in xuanfeiList" :key="key"
							@click="toXuanfeiProfile(v)">
							<van-image class="cover_img" round :src="common.getImageUrl(v.img_url)">
								<template v-slot:loading>
									<van-loading type="circular" />
								</template>
							</van-image>
							<div class="movie-list-item-bottom">
								<div class="movie-time-div">
									<span>{{v.xuanfei_name}}</span>
									<span>{{$t('index.performance')}}</span>
								</div>
							</div>
						</div>
						<div class="hot-recommend-more" @click="gotoMenu('/Choose')">{{$t('index.more')}}</div>
					</div>
				</div>
			</van-pull-refresh>
		</div>

		<!-- 系统公告弹窗 -->
		<van-popup
			v-model="showPopup"
			:close-on-click-overlay="true"
			round
			closeable
			close-icon-position="top-right"
			class="announcement-popup-container"
		>
			<div class="announcement-popup-content" v-if="popupInfo">
				<!-- 公告标题 -->
				<div class="announcement-header">
					<div class="announcement-icon">
						<van-icon name="volume-o" />
					</div>
					<h3 class="announcement-title">{{ popupInfo.title }}</h3>
				</div>

				<!-- 公告内容 -->
				<div class="announcement-body">
					<div class="announcement-content">
						{{ popupInfo.content }}
					</div>
				</div>

				<!-- 公告按钮 -->
				<div class="announcement-footer">
					<van-button
						type="primary"
						class="announcement-btn"
						@click="handleAnnouncementClick"
					>
						{{ popupInfo.button_text || '我知道了' }}
					</van-button>
				</div>
			</div>
		</van-popup>
	</div>
</template>

<script>
	export default {
		data() {
			return {
				notice: this.$t("index.loading"),
				banners: [{}],
				basicData: [],

				movielist_0: [{}, {}, {}, {}],
				movielist_1: [{}, {}, {}, {}, {}, {}, {}, {}],
				xuanfeiList: [{}, {}, {}, {}, {}, {}, {}, {}],
				isLoading: false,
				// 弹窗相关数据
				showPopup: false,
				popupInfo: null,
				movielistSwiperOption: {
					slidesPerView: 'auto',
					spaceBetween: 0,
					slidesPerGroup: 1,
				},
				bannerSwiperOption: {
					effect: 'coverflow',
					grabCursor: true,
					centeredSlides: true,
					slidesPerView: 'auto',
					speed: 800,
					autoplay: true,
					coverflowEffect: {
						rotate: 50,
						stretch: 10,
						depth: 100,
						modifier: 1,
						slideShadows: true
					}
				}
			};
		},
		methods: {
			gotoMenu(router) {
				this.$router.replace(router)
			},

			toPlayVideo(id) {
				if (!localStorage.getItem('token')) {
					this.$router.push({
						path: '/Auth'
					})
				} else {
					this.$router.push({
						path: '/PlayVideo?id=' + id
					})
				}
			},
			toXuanfeiProfile(xuanfeiData) {
				if (!localStorage.getItem('token')) {
					this.$router.push({
						path: '/Auth'
					})
				} else {
					// 跳转到选妃详情页面，传递必要的参数
					this.$router.push({
						path: '/profile?id=' + xuanfeiData.id + '&name=' + xuanfeiData.city_name + '&adsid=' + xuanfeiData.city_id
					})
				}
			},
			onRefresh() {
				setTimeout(() => {
					this.getBasicConfig();
					this.isLoading = false;
					this.$toast(this.$t("reservation.refresh"));
				}, 500);
			},
			getBasicConfig() {
				this.$http({
					method: 'get',
					url: 'sys_config'
				}).then(res => {
					this.basicData = res.data;
					console.info(res)
					this.getNotice(this.basicData); //获取公告
					this.getBanner(this.basicData); //获取banner

					this.getMovieList_0(this.basicData); //获取首页视频0
					this.getMovieList_1(this.basicData); //获取首页视频1
					this.getXuanfeiList(); //获取选妃列表
				})

			},
			getNotice(data) {
				this.notice = data.notice;
			},

			getMovieList_0(data) {
				this.movielist_0 = data.movielist_0
			},
			getMovieList_1(data) {
				this.movielist_1 = data.movielist_1
			},
			getBanner(data) {
				this.banners = data.banners;
			},
			getXuanfeiList() {
				// 首先获取地址列表
				this.$http({
					method: 'get',
					url: 'address_list'
				}).then(res => {
					if (res.data && res.data.length > 0) {
						// 获取多个城市的选妃数据
						const cityPromises = res.data.slice(0, 3).map(city => {
							return this.$http({
								method: 'get',
								url: 'xuanfeilist',
								data: { id: city.id }
							}).then(xuanfeiRes => {
								return xuanfeiRes.data.map(item => ({
									...item,
									city_id: city.id,
									city_name: city.name
								}));
							}).catch(err => {
								console.log(`获取城市${city.name}选妃列表失败:`, err);
								return [];
							});
						});

						// 等待所有城市数据获取完成
						Promise.all(cityPromises).then(allXuanfeiData => {
							// 合并所有城市的选妃数据
							const allXuanfei = allXuanfeiData.flat();

							// 随机打乱数组并取前8个
							const shuffled = allXuanfei.sort(() => 0.5 - Math.random());
							this.xuanfeiList = shuffled.slice(0, 8);

							console.log('首页选妃列表:', this.xuanfeiList);
						});
					}
				}).catch(err => {
					console.log('获取地址列表失败:', err);
				});
			},
			// 获取系统公告弹窗信息
			getPopupInfo() {
				this.$http({
					method: 'get',
					url: 'popup_info'
				}).then(res => {
					console.log('系统公告API响应:', res);
					if (res.code === 200 && res.data && res.data.enabled === 1) {
						this.popupInfo = res.data;
						this.showPopup = true;
					}
				}).catch(err => {
					console.log('获取系统公告失败:', err);
				});
			},
			// 处理公告弹窗点击事件
			handleAnnouncementClick() {
				// 关闭弹窗
				this.showPopup = false;
				// 可以在这里添加其他逻辑，比如记录用户已读状态
				console.log('用户已阅读系统公告');
			}
		},
		mounted() {

		},
		created() {
			this.getBasicConfig();
			// 获取弹窗信息
			this.getPopupInfo();
		}
	}
</script>

<style lang='less' scoped>
	@notice-bar-size: 30px;
	@movie-list-item-bottom-size: 25px;

	.linear-bg {
		height: 200px;
		background: linear-gradient(270deg, #988fba, #f487e0);
	}

	.home-container {
		position: absolute !important;
		top: 0;
		left: 0;
		right: 0;
		background-color: #ffffff;
	}

	.linear-gradient {
		width: 100%;
		height: 2px;
		background: linear-gradient(to right, rgba(126, 86, 120, 0), rgb(230, 195, 161), rgba(126, 86, 120, 0));
	}

	.notice-content {
		background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 50%, #fecfef 100%);
		margin: 0 25px;
		border-radius: 15px;
		padding: 15px 20px;
		box-shadow: 0 4px 15px rgba(244, 135, 224, 0.3);
	}

	.notice-header {
		display: flex;
		align-items: center;
		margin-bottom: 8px;
	}

	.notice-icon {
		color: #fff;
		font-size: 20px;
		margin-right: 8px;
	}

	.notice-title {
		color: #fff;
		font-size: 18px;
		font-weight: bold;
		margin-right: 8px;
	}

	.notice-subtitle {
		color: rgba(255, 255, 255, 0.8);
		font-size: 14px;
	}

	.notice-text {
		color: #333;
		font-size: 14px;
		line-height: 1.4;
		background: rgba(255, 255, 255, 0.9);
		padding: 10px;
		border-radius: 8px;
		margin-top: 8px;
	}

	.banner {
		width: 100%;
		margin-top: -23%;
	}

	.banner_swiper {
		height: 100%;
		width: 100%;

		.swiper-slide {
			border-radius: 10px;
			display: flex;
			justify-content: center;
			align-items: center;
			width: 620px;
			height: 300px;
			text-align: center;
			font-weight: bold;
			font-size: 20px;
			background-color: #ffffff;
			background-position: center;
			background-size: cover;
			color: #ffffff;
		}
	}

	::v-deep .swiper-container-3d .swiper-slide-shadow-left {
		background-image: linear-gradient(to left, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0));
	}

	::v-deep .swiper-container-3d .swiper-slide-shadow-right {
		background-image: linear-gradient(to right, rgba(0, 0, 0, 0), rgba(0, 0, 0, 0));
	}

	.banner_img {
		border-radius: 10px;
		width: 100%;
		height: 100%;
	}

	.hot-game {
		width: 100%;
		height: 100%;
	}

	.hot-title-div {
		width: calc(100% - 50px);
		margin: 0 auto;
		height: 80px;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.hot-title-div>div:first-child {
		width: 430px;
	}

	.hot-title-div div {
		display: flex;
		align-items: center;
		justify-content: flex-start;
	}

	.hot-title-div>div:nth-child(2) span {
		font-size: 20px;
		color: #979799;
	}

	.hot-title-div>div:first-child span {
		display: flex;
		align-items: center;
		justify-content: flex-start;
		font-size: 10px;
		font-weight: 700;
		color: #000;
	}

	.hot-title-div>div:first-child span {
		display: flex;
		align-items: center;
		justify-content: flex-start;
		font-size: 28px;
		font-weight: 700;
		color: #000;
	}

	.hot-title-div>div:nth-child(2) span {
		font-size: 25px;
		color: #979799;
	}

	.hot-title-div>div:first-child span:before {
		content: "";
		display: block;
		width: 5px;
		height: 30px;
		background-color: #f487e0;
		border-radius: 1px;
		margin-right: 5px;
	}



	.hot-recommend {
		width: 100%;
		flex: 1;
		background-color: #f2f2f5;
	}

	.movie_swiper {
		.swiper-slide {
			width: 80%;
		}
	}

	.movie_list_0 {
		width: calc(100% - 50px);
		margin: 0 auto;
	}

	.movie_cover {
		border-radius: 10px;
		width: 550px;
		height: 330px
	}

	.movie_list_0 .movie-list-item-bottom {
		position: relative;
		width: 550px;
		bottom: 43px;
	}

	.movie_list_0 .movie-list-item-bottom .movie-time-div {
		background-color: rgba(0, 0, 0, .4);
	}

	.movie_list_0 .movie-list-item-bottom>div {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.movie_list_0 .movie-list-item-bottom .movie-time-div .van-count-down {
		font-size: 28px;
		color: #fff;
	}

	.movie_list_0 .movie-time-div {
		color: #fff;
		border-radius: 0 0 10px 10px;
	}

	.movie_list_0 .movie_swiper .hot {
		position: absolute;
		top: 0px;
		left: 0px;
		width: 80px;
	}

	.movie_list_0 span {
		font-size: 30px;
	}

	.movie_list_1 {
		display: flex;
		width: calc(100% - 50px);
		margin: 0 auto;
		align-items: flex-start;
		justify-content: flex-start;
		flex-wrap: wrap;
	}

	.movie_list_1 .movie-list-item .cover_img {
		border-radius: 10px;
		width: 335px;
		height: 290px;
	}

	.home-scroll {
		padding-bottom: 110px;
	}

	.movie_list_1 .movie-list-item {
		margin-bottom: -10px;
	}

	.movie_list_1 .movie-list-item-bottom {
		position: relative;
		width: 335px;
		bottom: 42px;

	}

	.movie_list_1 .movie-list-item:nth-child(odd) {
		margin-right: 25px;
	}

	.movie_list_1 .movie-list-item-bottom .movie-time-div {
		background-color: rgba(0, 0, 0, .4);
	}

	.movie_list_1 .movie-list-item-bottom>div {
		width: 100%;
		display: flex;
		align-items: center;
		justify-content: space-between;
	}

	.movie_list_1 .movie-list-item-bottom .movie-time-div .van-count-down {
		color: #fff;
	}

	.movie_list_1 .movie-time-div {
		color: #fff;
		border-radius: 0 0 10px 10px;
		height: 35px;
	}

	.movie_list_1 .movie_swiper .hot {
		position: absolute;
		top: 0px;
		left: 0px;
		width: 5px;
	}

	.movie_list_1 .movie-list-item .movie-time-div span:first-child {
		overflow: hidden;
		white-space: nowrap;
		width: 180px;
		padding-left: 8px;
		font-size: 25px;
	}

	.movie_list_1 .movie-list-item .movie-time-div span:last-child {
		overflow: hidden;
		white-space: nowrap;
		width: 0px;
		padding-right: 110px;
		font-size: 22px;
	}

	.movie_list_0 .movie-time-div span:first-child {
		overflow: hidden;
		white-space: nowrap;
		width: 350px;
		padding-left: 10px;
		font-size: 25px;
	}

	.hot-recommend-more {
		width: 100%;
		padding-bottom: 20px;
		text-align: center;
		color: #979799;
		font-size: 30px;
	}

	.hot-items-div .game_item_img {
		width: 100px;
		height: 100px;
	}

	::v-deep .hot-items-div .game_item_img .van-image__img {
		border-radius: 20px;
	}

	::v-deep .van-pull-refresh__track .van-pull-refresh__head * {
		color: #000000;
		font-size: 35px;
	}

	/* 系统公告弹窗样式 */
	::v-deep .announcement-popup-container {
		background: transparent;
		max-width: 85%;
		max-height: 70%;
	}

	.announcement-popup-content {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-radius: 20px;
		overflow: hidden;
		position: relative;
		box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
	}

	.announcement-header {
		background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
		padding: 20px;
		text-align: center;
		position: relative;
	}

	.announcement-icon {
		margin-bottom: 10px;
	}

	.announcement-icon .van-icon {
		font-size: 32px;
		color: #fff;
		animation: pulse 2s infinite;
	}

	@keyframes pulse {
		0% { transform: scale(1); }
		50% { transform: scale(1.1); }
		100% { transform: scale(1); }
	}

	.announcement-title {
		color: #fff;
		font-size: 20px;
		font-weight: bold;
		margin: 0;
		text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
	}

	.announcement-body {
		padding: 25px 20px;
		background: #fff;
	}

	.announcement-content {
		color: #333;
		font-size: 16px;
		line-height: 1.6;
		text-align: left;
		max-height: 300px;
		overflow-y: auto;
		padding: 10px;
		background: #f8f9fa;
		border-radius: 10px;
		border-left: 4px solid #f5576c;
	}

	.announcement-footer {
		padding: 20px;
		text-align: center;
		background: #fff;
		border-top: 1px solid #eee;
	}

	.announcement-btn {
		width: 80%;
		height: 50px;
		font-size: 18px;
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border: none;
		border-radius: 25px;
		color: white;
		font-weight: bold;
		box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
		transition: all 0.3s ease;
	}

	.announcement-btn:hover {
		transform: translateY(-2px);
		box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
	}

	::v-deep .announcement-popup-container .van-popup__close-icon {
		color: #fff;
		background: rgba(0, 0, 0, 0.6);
		border-radius: 50%;
		width: 32px;
		height: 32px;
		display: flex;
		align-items: center;
		justify-content: center;
		font-size: 18px;
		top: 10px;
		right: 10px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
	}
</style>