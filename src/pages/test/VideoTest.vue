<template>
  <div class="video-test-page">
    <van-nav-bar title="视频播放测试" class="nav-bar">
      <template #left>
        <van-icon name="arrow-left" color="#fff" @click="$router.go(-1)"/>
      </template>
    </van-nav-bar>
    
    <div class="test-container">
      <h3>视频播放测试</h3>
      
      <!-- 测试不同格式的视频URL -->
      <div class="test-section">
        <h4>测试视频URL处理</h4>
        <div class="url-test" v-for="(test, index) in testUrls" :key="index">
          <p><strong>原始URL:</strong> {{ test.original }}</p>
          <p><strong>处理后URL:</strong> {{ common.getVideoUrl(test.original) }}</p>
          <p><strong>检测类型:</strong> {{ common.getVideoType(test.original) }}</p>
          <hr>
        </div>
      </div>
      
      <!-- 实际视频播放测试 -->
      <div class="test-section" v-if="testVideoUrl">
        <h4>实际播放测试</h4>
        <video
          ref="testVideo"
          :src="common.getVideoUrl(testVideoUrl)"
          controls
          preload="metadata"
          class="test-video"
          playsinline
          webkit-playsinline
          @loadedmetadata="onVideoLoaded"
          @error="onVideoError"
          @canplay="onCanPlay"
        >
          <source :src="common.getVideoUrl(testVideoUrl)" :type="common.getVideoType(testVideoUrl)">
          您的浏览器不支持视频播放
        </video>
        
        <div class="test-info">
          <p><strong>测试视频URL:</strong> {{ testVideoUrl }}</p>
          <p><strong>处理后URL:</strong> {{ common.getVideoUrl(testVideoUrl) }}</p>
          <p><strong>视频类型:</strong> {{ common.getVideoType(testVideoUrl) }}</p>
          <p><strong>状态:</strong> {{ videoStatus }}</p>
        </div>
      </div>
      
      <!-- 输入自定义URL测试 -->
      <div class="test-section">
        <h4>自定义URL测试</h4>
        <van-field
          v-model="customUrl"
          label="视频URL"
          placeholder="请输入视频URL"
          type="textarea"
          rows="3"
        />
        <van-button @click="testCustomUrl" type="primary" class="test-button">
          测试播放
        </van-button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      testUrls: [
        { original: '/uploads/video/test.mp4' },
        { original: 'http://example.com/video.mp4' },
        { original: 'https://jsdao.cc/uploads/video/sample.mp4' },
        { original: '/uploads/video/test.webm' },
        { original: '/uploads/video/stream.m3u8' },
        { original: 'video.mp4' }
      ],
      testVideoUrl: '',
      customUrl: '',
      videoStatus: '未加载'
    };
  },
  methods: {
    onVideoLoaded(event) {
      this.videoStatus = `已加载 - 时长: ${Math.round(event.target.duration)}秒`;
      console.log('测试视频加载完成:', event.target.duration);
    },
    
    onVideoError(event) {
      const error = event.target.error;
      if (error) {
        this.videoStatus = `错误 - 代码: ${error.code}, 信息: ${error.message}`;
        console.error('测试视频错误:', error);
      } else {
        this.videoStatus = '未知错误';
      }
    },
    
    onCanPlay() {
      this.videoStatus = '可以播放';
      console.log('测试视频可以播放');
    },
    
    testCustomUrl() {
      if (!this.customUrl.trim()) {
        this.$toast('请输入视频URL');
        return;
      }
      
      this.testVideoUrl = this.customUrl.trim();
      this.videoStatus = '加载中...';
      
      // 重置视频元素
      this.$nextTick(() => {
        if (this.$refs.testVideo) {
          this.$refs.testVideo.load();
        }
      });
    }
  },
  
  mounted() {
    // 设置一个默认的测试视频URL（如果有的话）
    // this.testVideoUrl = '/uploads/video/sample.mp4';
  }
};
</script>

<style scoped>
.video-test-page {
  min-height: 100vh;
  background: #f5f5f5;
}

.nav-bar {
  background: linear-gradient(to right, #7f5778, #e5c2a0);
}

.test-container {
  padding: 20px;
}

.test-section {
  background: white;
  margin-bottom: 20px;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.test-section h4 {
  margin-top: 0;
  color: #333;
  border-bottom: 2px solid #7f5778;
  padding-bottom: 5px;
}

.url-test {
  margin-bottom: 15px;
  padding: 10px;
  background: #f9f9f9;
  border-radius: 4px;
}

.url-test p {
  margin: 5px 0;
  word-break: break-all;
}

.test-video {
  width: 100%;
  max-width: 500px;
  height: auto;
  border-radius: 4px;
}

.test-info {
  margin-top: 10px;
  padding: 10px;
  background: #f0f0f0;
  border-radius: 4px;
}

.test-info p {
  margin: 5px 0;
  word-break: break-all;
}

.test-button {
  margin-top: 10px;
  width: 100%;
}

hr {
  border: none;
  border-top: 1px solid #eee;
  margin: 10px 0;
}
</style>
