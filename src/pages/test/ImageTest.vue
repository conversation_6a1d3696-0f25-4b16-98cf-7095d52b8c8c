<template>
  <div class="image-test-page">
    <van-nav-bar title="图片加载测试" class="nav-bar">
      <template #left>
        <van-icon name="arrow-left" color="#fff" @click="$router.go(-1)"/>
      </template>
    </van-nav-bar>
    
    <div class="test-container">
      <h3>图片URL处理测试</h3>
      
      <!-- 测试不同格式的图片URL -->
      <div class="test-section">
        <h4>URL处理测试</h4>
        <div class="url-test" v-for="(test, index) in testUrls" :key="index">
          <p><strong>原始URL:</strong> {{ test.original }}</p>
          <p><strong>处理后URL:</strong> {{ common.getImageUrl(test.original) }}</p>
          <div class="image-preview">
            <img 
              :src="common.getImageUrl(test.original)" 
              :alt="test.description"
              @load="onImageLoad(test.original)"
              @error="onImageError(test.original)"
              class="test-image"
            />
            <span class="status" :class="getStatusClass(test.original)">
              {{ getImageStatus(test.original) }}
            </span>
          </div>
          <hr>
        </div>
      </div>
      
      <!-- 实际图片加载测试 -->
      <div class="test-section">
        <h4>实际图片测试</h4>
        <div class="image-grid">
          <div class="image-item" v-for="(img, index) in sampleImages" :key="index">
            <img 
              :src="common.getImageUrl(img.url)" 
              :alt="img.description"
              @load="onImageLoad(img.url)"
              @error="onImageError(img.url)"
              class="sample-image"
            />
            <p>{{ img.description }}</p>
            <span class="status" :class="getStatusClass(img.url)">
              {{ getImageStatus(img.url) }}
            </span>
          </div>
        </div>
      </div>
      
      <!-- 输入自定义URL测试 -->
      <div class="test-section">
        <h4>自定义URL测试</h4>
        <van-field
          v-model="customUrl"
          label="图片URL"
          placeholder="请输入图片URL"
          type="textarea"
          rows="2"
        />
        <van-button @click="testCustomUrl" type="primary" class="test-button">
          测试图片
        </van-button>
        
        <div v-if="customTestResult" class="custom-result">
          <p><strong>原始URL:</strong> {{ customUrl }}</p>
          <p><strong>处理后URL:</strong> {{ common.getImageUrl(customUrl) }}</p>
          <div class="image-preview">
            <img 
              :src="common.getImageUrl(customUrl)" 
              alt="自定义测试图片"
              @load="onCustomImageLoad"
              @error="onCustomImageError"
              class="test-image"
            />
            <span class="status" :class="customImageStatus.class">
              {{ customImageStatus.text }}
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      testUrls: [
        { original: '/uploads/images/test.jpg', description: '相对路径' },
        { original: 'http://example.com/image.jpg', description: 'HTTP链接' },
        { original: 'https://jsdao.cc/uploads/images/sample.jpg', description: 'HTTPS链接' },
        { original: 'uploads/images/test.png', description: '无斜杠相对路径' },
        { original: '', description: '空URL' },
        { original: null, description: 'null值' }
      ],
      sampleImages: [
        { url: '/img/login/login-bg.png', description: '登录背景' },
        { url: '/img/footer/home.jpg', description: '底部图标' },
        { url: '/img/null.png', description: '默认图片' }
      ],
      customUrl: '',
      customTestResult: false,
      customImageStatus: { text: '未测试', class: 'pending' },
      imageStatuses: {} // 存储每个图片的加载状态
    };
  },
  methods: {
    onImageLoad(url) {
      this.$set(this.imageStatuses, url, { text: '加载成功', class: 'success' });
    },
    
    onImageError(url) {
      this.$set(this.imageStatuses, url, { text: '加载失败', class: 'error' });
    },
    
    onCustomImageLoad() {
      this.customImageStatus = { text: '加载成功', class: 'success' };
    },
    
    onCustomImageError() {
      this.customImageStatus = { text: '加载失败', class: 'error' };
    },
    
    getImageStatus(url) {
      return this.imageStatuses[url] ? this.imageStatuses[url].text : '加载中...';
    },
    
    getStatusClass(url) {
      return this.imageStatuses[url] ? this.imageStatuses[url].class : 'loading';
    },
    
    testCustomUrl() {
      if (!this.customUrl.trim()) {
        this.$toast('请输入图片URL');
        return;
      }
      
      this.customTestResult = true;
      this.customImageStatus = { text: '加载中...', class: 'loading' };
    }
  }
};
</script>

<style scoped>
.image-test-page {
  min-height: 100vh;
  background: #f5f5f5;
}

.nav-bar {
  background: linear-gradient(to right, #7f5778, #e5c2a0);
}

.test-container {
  padding: 20px;
}

.test-section {
  background: white;
  margin-bottom: 20px;
  padding: 15px;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.test-section h4 {
  margin-top: 0;
  color: #333;
  border-bottom: 2px solid #7f5778;
  padding-bottom: 5px;
}

.url-test {
  margin-bottom: 15px;
  padding: 10px;
  background: #f9f9f9;
  border-radius: 4px;
}

.url-test p {
  margin: 5px 0;
  word-break: break-all;
  font-size: 14px;
}

.image-preview {
  margin-top: 10px;
  text-align: center;
}

.test-image {
  max-width: 200px;
  max-height: 150px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.image-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 15px;
}

.image-item {
  text-align: center;
  padding: 10px;
  background: #f9f9f9;
  border-radius: 4px;
}

.sample-image {
  width: 100%;
  max-width: 120px;
  height: 80px;
  object-fit: cover;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.status {
  display: inline-block;
  margin-top: 5px;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
}

.status.success {
  background: #4caf50;
  color: white;
}

.status.error {
  background: #f44336;
  color: white;
}

.status.loading {
  background: #ff9800;
  color: white;
}

.status.pending {
  background: #9e9e9e;
  color: white;
}

.test-button {
  margin-top: 10px;
  width: 100%;
}

.custom-result {
  margin-top: 15px;
  padding: 10px;
  background: #f0f0f0;
  border-radius: 4px;
}

hr {
  border: none;
  border-top: 1px solid #eee;
  margin: 10px 0;
}
</style>
