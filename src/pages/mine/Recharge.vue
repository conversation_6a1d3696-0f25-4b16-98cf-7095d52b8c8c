<template>
  <div class="container page">
    <div class="header">
      <van-nav-bar :title="$t('recharge.recharge')" class="nav-bar">
        <template #left>
          <van-icon name="arrow-left" color="#fff" @click="back()" />
        </template>
      </van-nav-bar>
      <div class="info">
        <p class="title">
          {{ $t("recharge.curr_balance") }}({{ $t("reservation.unit") }})
        </p>
        <p class="value">{{ formatMoney(this.balance) }}</p>
      </div>
      <div class="content recharge">
        <van-form @submit="onSubmit">
          <div class="form-item">
            <div class="form-item-title">{{ $t("recharge.input_money") }}</div>
            <div style="height: 65px">
              <van-field
                v-model="money"
                name="money"
                label="HKD"
                :placeholder="$t('recharge.input_money')"
              />
            </div>
          </div>
          <div class="form-item">
            <div class="form-item-title">{{ $t("recharge.select_network") }}</div>
            <div>
              <van-radio-group v-model="network">
                <van-radio name="TRC-20">TRC-20 (USDT)</van-radio>
              </van-radio-group>
            </div>
            <div style="margin: 16px">
              <van-button round block type="info" native-type="submit"
                >Siguiente</van-button
              >
            </div>
          </div>
        </van-form>
      </div>
    </div>
  </div>
</template>
  
<script>
import Vue from "vue";
import { Form } from "vant";
import { Field } from "vant";
import { RadioGroup, Radio } from "vant";
Vue.use(Form).use(Field).use(RadioGroup).use(Radio);
export default {
  data() {
    return {
      balance: 0,
      network: "TRC-20", // 显示用，实际使用EPUSDT
      win_money: 0,
      money: "",
      personalreport: {},
    };
  },
  mounted() {
    this.balance = this.$route.params.balance;
  },
  methods: {
    // 格式化金额为香港格式
    formatMoney(amount) {
      if (!amount && amount !== 0) return '0';
      const num = parseFloat(amount);
      if (isNaN(num)) return '0';

      // 如果是整数，不显示小数点
      if (num % 1 === 0) {
        return num.toLocaleString('en-HK');
      } else {
        return num.toLocaleString('en-HK', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        });
      }
    },
    back() {
      return window.history.back();
    },

    //创建充值订单
    onSubmit(values) {
      const money = values.money;
      if (money <= 0) {
        this.$toast(this.$t("reservation.money_err"));
        return;
      }

      // 显示加载提示
      this.$toast.loading({
        message: this.$t('recharge.creating_order'),
        forbidClick: true,
        duration: 0
      });

      // 调试信息
      console.log('充值请求参数:', {
        amount: parseFloat(money),
        pay_way: "Epusdt"
      });
      console.log('当前用户token:', localStorage.getItem('token'));

      this.$http({
        method: "post",
        data: {
          amount: parseFloat(money),
          pay_way: "Epusdt"
        },
        url: "recharge_create_order",
      }).then((res) => {
        this.$toast.clear();
        console.log(res);
        if (res.code === 200) {
          // 在当前窗口跳转到支付页面
          window.location.href = res.data.pay_url;

          // 注意：由于页面会跳转，下面的提示可能不会显示
          // this.$toast.success(this.$t('recharge.order_created'));

          // 可选：开始轮询订单状态
          // this.pollOrderStatus(res.data.order_no);
        } else {
          this.$toast(res.message || res.msg || this.$t('recharge.create_order_error'));
        }
      }).catch((error) => {
        this.$toast.clear();
        console.error('创建订单失败:', error);
        this.$toast(this.$t('recharge.network_error'));
      });
    },

    // 轮询订单状态（可选功能）
    pollOrderStatus(orderNo) {
      const maxAttempts = 60; // 最多轮询60次（5分钟）
      let attempts = 0;

      const poll = () => {
        this.$http({
          method: "post",
          data: {
            order_no: orderNo
          },
          url: "recharge_get_order_status",
        }).then((res) => {
          if (res.code === 200) {
            const order = res.data;

            if (order.status === 2) {
              // 支付成功
              this.$toast.success(this.$t('recharge.payment_success'));
              // 刷新用户余额
              this.getPersonalreport();
              return;
            } else if (order.status === 3) {
              // 支付失败
              this.$toast.fail(this.$t('recharge.payment_failed'));
              return;
            }
          }

          attempts++;
          if (attempts < maxAttempts) {
            // 5秒后再次查询
            setTimeout(poll, 5000);
          }
        }).catch((error) => {
          console.error('查询订单状态失败:', error);
        });
      };

      // 延迟3秒开始轮询，给用户时间完成支付
      setTimeout(poll, 3000);
    },

    getPersonalreport() {
      this.$http({
        method: "get",
        url: "user_get_personalreport",
      }).then((res) => {
        if (res.code === 200) {
          this.personalreport = res.data;
          this.win_money =
            this.personalreport.win_money - this.personalreport.play_money;
        } else if (res.code === 401) {
          this.$toast(res.msg);
        }
      });
    },
  },
  created() {
    if (!localStorage.getItem("token")) {
      this.$router.push({ path: "/Login" });
    } else {
      this.getPersonalreport();
    }
  },
};
</script>
  
  <style lang='less' scoped>
@import "../../assets/css/base.css";
.container .header {
  background: linear-gradient(270deg, #e6c3a1, #7e5678);
}
.recharge {
  padding: 10px 30px;
}

.van-cell {
  line-height: 65px !important;
}

/* 增加输入框字体大小 */
/deep/.van-field__control {
  font-size: 40px !important;
  color: #333 !important;
}

/deep/.van-field__label {
  font-size: 36px !important;
  color: #666 !important;
}

/* 调整提示文字大小，确保完全显示 */
/deep/.van-field__control::placeholder {
  font-size: 20px !important;
  color: #999 !important;
  line-height: 1.2 !important;
}

/* 调整输入框高度以适应文字 */
/deep/.van-field {
  min-height: 90px !important;
}

/deep/.van-cell {
  min-height: 90px !important;
  padding: 20px 16px !important;
  align-items: center !important;
}

/deep/.van-field__control {
  line-height: 1.3 !important;
}

.van-button {
  height: 87px !important;
}
.van-button__text {
  color: #fff !important;
}

/deep/.van-radio__icon {
  font-size: 30px !important;
}
/deep/.van-radio__label {
  margin-left: 25px !important;
  font-size: 35px !important;
}

/deep/.van-radio {
  height: 65px !important;
}

.form-item {
  margin-top: 40px;
}

.form-item-title {
  font-size: 36px;
  font-weight: bold;
  color: #999;
  margin-bottom: 20px;
}

.recharge span {
  font-size: 4vw;
  color: #868686;
  font-weight: 500;
}

.container .header .info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding-bottom: 20px;
  padding-top: 10px;
  margin: auto;
}
.container .header .info .title {
  font-size: 25px;
  color: #e5e5e5;
}
.container .header .info .value {
  margin: 10px auto;
  color: #fff;
  font-size: 50px;
  border-bottom: 1px solid #fff;
}
.container .header .info .tip {
  font-size: 30px;
  color: #e5e5e5;
}
.container .content {
  flex: 1;
  background: #f2f2f5;
}
</style>
  