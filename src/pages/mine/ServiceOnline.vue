<template>
	<div class="container page">
		<div class="header">
			<van-nav-bar :title="$t('my.online_service')" class="nav-bar">
				<template #left>
					<van-icon name="arrow-left" color="#fff" @click="back()" />
				</template>
			</van-nav-bar>
		</div>
		<div class="servicelistItem">
			<div class="servicelistItemTop">
				<img class="servicelistItemImage" src="img/mine/kefu.png">
				<div class="servicelistItemText">
					{{this.$store.getters.getBaseInfo.name !==undefined ?this.$store.getters.getBaseInfo.name:this.$t("my.title")}}
				</div>
				<div class="servicelistItemBtn" @click="toServicePage()">
					<div class="servicelistItemBtnText">
						{{$t("my.contact")}}
					</div>
				</div>
			</div>
			<div class="servicelistItemBottom">
				<div class="servicelistItemInfoText">
					{{$t("my.service_time")}}
				</div>
			</div>
		</div>
		<!-- USDT充值入口 -->
		<div class="usdt-recharge-section">
			<div class="usdt-recharge-item" @click="toUSDTRecharge()">
				<div class="usdt-icon">
					<span class="usdt-text">₮</span>
				</div>
				<div class="usdt-content">
					<div class="usdt-title">Recarga USDT</div>
					<div class="usdt-desc">Recarga rápida y segura con USDT</div>
				</div>
				<div class="usdt-arrow">
					<van-icon name="arrow" />
				</div>
			</div>
		</div>
	</div>
</template>

<script>
	export default {
		data() {
			return {
				config:{},
			};
		},
		created() {
			this.$http({
				method: 'get',
				url: 'base_info'
			}).then(res => {
				this.config = res.data
			})
		},
		methods: {
			back() {
				return window.history.back();
			},
			toServicePage() {
				const service = this.$store.getters.getBaseInfo;
				console.log(service)
				if (service.iskefu == 1) {
					console.log('ssss')
					window.location.href = service.kefu
				}
				// this.$router.push("ServicePage");
			},
			toUSDTRecharge() {
				// 跳转到USDT充值页面
				this.$router.push('/Recharge?type=usdt');
			}
		}
	};
</script>

<style lang='less' scoped>
	@import "../../assets/css/base.css";

	/deep/ .van-hairline--top-bottom {
		padding: 25px 20px;
		margin: 20px 0;
	}

	/deep/ .van-collapse-item {
		border-radius: 15px;
		overflow: hidden;
		margin: 20px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
	}

	/deep/ .van-cell__title {
		font-size: 30px;
		padding: 20px 0px;
		font-weight: 600;
		color: #333;
	}

	/deep/ .van-collapse-item__content{
		font-size: 28px !important;
		line-height: 1.6 !important;
		padding: 25px 20px !important;
		color: #666;
	}

	/deep/ .van-icon__image{
		width: 3.5em;
		height: 3.5em;
	}

	.usdt-recharge-section {
		margin: 20px;
	}

	.usdt-recharge-item {
		display: flex;
		align-items: center;
		padding: 30px 25px;
		background: #fff;
		border-radius: 15px;
		box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
		cursor: pointer;
		transition: all 0.3s ease;
	}

	.usdt-recharge-item:hover {
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
		transform: translateY(-2px);
	}

	.usdt-icon {
		width: 60px;
		height: 60px;
		margin-right: 20px;
		display: flex;
		align-items: center;
		justify-content: center;
		background: linear-gradient(135deg, #26a69a, #00695c);
		border-radius: 50%;
	}

	.usdt-text {
		color: white;
		font-size: 32px;
		font-weight: bold;
	}

	.usdt-content {
		flex: 1;
	}

	.usdt-title {
		font-size: 32px;
		font-weight: 600;
		color: #333;
		margin-bottom: 8px;
	}

	.usdt-desc {
		font-size: 26px;
		color: #666;
		line-height: 1.4;
	}

	.usdt-arrow {
		color: #999;
		font-size: 24px;
	}

	.servicelistItem {
		display: flex;
		flex-direction: column;
		height: 240px;
		padding: 40px 30px;
		margin: 30px 20px;
		border-radius: 20px;
		justify-content: space-between;
		background: #fff;
		box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
	}

	.servicelistItem .servicelistItemTop {
		display: flex;
		flex-direction: row;
		align-items: center;
		justify-content: space-between;
		height: 120px;
		margin-bottom: 20px;
	}

	.servicelistItem .servicelistItemTop .servicelistItemImage {
		width: 90px;
		height: 90px;
		border-radius: 50%;
		margin-right: 20px;
	}

	.servicelistItem .servicelistItemTop .servicelistItemText {
		margin-left: 20px;
		font-size: 36px;
		font-weight: 600;
		color: #333;
		flex: 1;
		line-height: 1.3;
	}

	.servicelistItem .servicelistItemTop .servicelistItemBtn {
		display: flex;
		width: 160px;
		height: 60px;
		border-radius: 30px;
		align-items: center;
		justify-content: center;
		background: linear-gradient(270deg, #e6c3a1, #7e5678);
		box-shadow: 0 2px 8px rgba(126, 86, 120, 0.3);
	}

	.servicelistItem .servicelistItemTop .servicelistItemBtn .servicelistItemBtnText {
		color: #fff;
		font-size: 28px;
		font-weight: 500;
	}

	.servicelistItem .servicelistItemBottom {
		display: flex;
		justify-content: center;
		align-items: center;
		height: 60px;
		background: #f8f9fa;
		border-radius: 15px;
		color: #666;
		margin-top: 10px;
	}

	.servicelistItem .servicelistItemBottom .servicelistItemInfoText {
		font-size: 26px;
		line-height: 1.4;
		text-align: center;
		padding: 0 15px;
	}
</style>