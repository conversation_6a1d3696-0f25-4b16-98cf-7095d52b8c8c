<template>
  <div class="container page">
    <van-nav-bar :title="$t('setting.fill_fps')" class="nav-bar">
      <template #left>
        <van-icon name="arrow-left" color="#fff" @click="back()"/>
      </template>
    </van-nav-bar>
    <div class="main-box">
      <div class="label">{{$t("setting.fill_fps_tip")}}</div>
      <van-cell-group>
        <van-field v-model="bank" :label="$t('setting.bank_name')" :placeholder="$t('setting.bank_name_tip')" />
        <van-field v-model="bankid" :label="$t('setting.fps_account')" :placeholder="$t('setting.fps_account_tip')" />
        <van-field v-model="username" :label="$t('setting.english_name')" :placeholder="$t('setting.english_name_place')" />
      </van-cell-group>
      <p>{{$t("setting.fps_warn")}}</p>
    </div>
    <van-button class="bindCard" type="default" @click="bindCard()">{{$t("setting.fps_ok")}}</van-button>
  </div>
</template>

<script>
export default {
  data() {
    return {
      userInfo:{},
      bankid:"", // 转数快/户口
      username:"", // 英文名
      bank:"", // 银行名称
    };
  },
  methods: {
    back(){
      return window.history.back();
    },
    bindCard(){
      // 检查是否已绑定
      if(this.userInfo.bankid){
        this.$toast(this.$t("setting.fps_account_tip"));
        return true;
      }

      // 验证银行名称
      if(this.bank === "" || this.bank === null || this.bank === undefined){
        this.$toast.fail(this.$t("setting.bank_name_tip"));
        return false;
      }

      // 验证转数快/户口
      if(this.bankid === ""){
        this.$toast.fail(this.$t("setting.fps_account_tip"));
        return false;
      }

      // 验证英文名
      if(this.username === ""){
        this.$toast(this.$t("setting.english_name_place"));
        return false;
      }

      this.$http({
        method: 'post',
        data:{
          bankid: this.bankid,
          bank: this.bank,
          username: this.username
        },
        url: 'user_set_bank'
      }).then(res=>{
        if(res.code === 200){
          this.$router.push({path:'/Mine'})
          this.$toast(res.msg);
        }else if(res.code ===401){
          this.$toast(res.msg);
        }
      })
    },
    getUserInfo(){
      this.$http({
        method: 'get',
        url: 'user_info'
      }).then(res=>{
        if(res.code === 200){
          this.userInfo = res.data;
          this.name = res.data.name;
        }else if(res.code ===401){
          this.$toast(res.msg);
        }
      })
    },
    getUserBankInfo(){
      this.$http({
        method: 'get',
        url: 'user_get_bank'
      }).then(res=>{
        if(res.code === 200){
          if(res.data.is_bank){
            this.is_bind = true;
          }else {
            this.is_bind = false;
          }
        }else if(res.code ===401){
          this.$toast(res.msg);
        }
      })
    }
  },
  created() {
    if(!localStorage.getItem('token')){
      this.$router.push({path:'/Login'})
    }else {
      this.getUserInfo();
      this.getUserBankInfo();
    }
  }
};
</script>

<style lang='less' scoped>
@import "../../assets/css/base.css";
.van-cell {
  font-size: 32px;
  line-height: 80px;
}
.van-hairline--bottom::after {
  border-bottom-width: 3px;
}
.bankbox{
  padding: 15px;
  color: #000;
  background-color: #fff;
}
.bankbox .title{
  padding: 8px 10px 20px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  font-size: 28px;
}
.main-box{
  background: #fff;

}
.main-box .label{
  padding: 20px;
  font-size: 35px;
  color: #797878;
}

.main-box p{
  padding: 0 20px;
  font-size: 30px;
  color: #ee0a24;
}
.bindCard {
  margin: 20px 30px 0;
  height: 80px;
  line-height: 1.22667rem;
  border-radius: 50px;
  color: #fff;
  font-size: 30px;
  font-weight: bolder;
  border: none;
  background: linear-gradient(
      90deg,#988fba,#f487e0);
}
</style>
