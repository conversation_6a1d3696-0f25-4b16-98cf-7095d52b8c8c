<template>
  <div class="container page">
    <div class="header">
      <van-nav-bar :title="$t('my.my_statement')" class="nav-bar">
        <template #left>
          <van-icon name="arrow-left" color="#fff" @click="back()"/>
        </template>
      </van-nav-bar>
      <div class="company-info">
        <div class="company-logo">
          <van-image :src="companyInfo.company_logo || 'img/mine/company-logo.png'" class="logo-img">
            <template v-slot:loading>
              <van-loading type="spinner" />
            </template>
          </van-image>
        </div>
        <h2 class="company-name">{{ companyInfo.company_name || $t('my.my_statement') }}</h2>
        <p class="company-slogan">{{ $t('my.professional_service') }}</p>
      </div>
    </div>

    <div class="content">
      <!-- 公司基本信息 - 只保留这一个部分 -->
      <div class="info-card">
        <h3 class="card-title">
          <van-icon name="info-o" />
          {{ $t('my.company_profile') }}
        </h3>
        <div class="company-description">
          <p class="section-title">1.📅Registro y Estructura Corporativa</p>
          <p class="content-text">Haotian Media Co.,Ltd.fue registrada oficialmente en los Estados Unidos el 18 de mayo de 2022.</p>
          <p class="content-text">Cuenta con 18 subsidiarias y 27 sucursales a nivel global.</p>
          <p class="content-text">Capital registrado:8 mil millones de dólares estadounidenses.</p>
          <p class="content-text">Activos fijos actuales:370 mil millones de dólares estadounidenses.</p>
          <p class="content-text">Dispone de oficinas y socios comerciales en ciudades clave y países como:</p>
          <p class="content-text">Los Ángeles,Nueva York,Hong Kong,Singapur,Taiwán,Shanghái,Japón,Corea del Sur,Italia,entre otros.</p>

          <p class="section-title">2.🎥Áreas de Negocio</p>
          <p class="content-text">La empresa se dedica a múltiples sectores dentro del ámbito de medios y entretenimiento,incluyendo:</p>
          <p class="content-text">Gestión operativa de socios comerciales(bajo aprobación y regulación de la empresa)</p>
          <p class="content-text">Producción audiovisual</p>
          <p class="content-text">Operación de clubes</p>
          <p class="content-text">Publicidad</p>
          <p class="content-text">Plataformas en línea y digitales</p>

          <p class="section-title">3.📝Protección Legal de Contratos</p>
          <p class="content-text">Todos los socios comerciales y acuerdos con la empresa están respaldados legalmente mediante:</p>
          <p class="content-text">Validación y confirmación de contratos por los Departamentos Jurídico y Comercial</p>
          <p class="content-text">Firma oficial y sello tanto por parte de las filiales como de los socios comerciales</p>
          <p class="content-text">Esto garantiza los derechos e intereses de todos los clientes y socios involucrados.</p>
          <p class="content-text">La empresa supervisa y regula el cumplimiento operativo de las filiales y comercios asociados.</p>

          <p class="section-title">4.🛡Sistema de Gestión y Protección al Cliente</p>
          <p class="content-text">Teniendo en cuenta las normativas específicas de cada país o región,Haotian Media ha establecido un Departamento de Quejas y Atención al Cliente en su sede central.</p>
          <p class="content-text">Cualquier violación de derechos por parte de socios o filiales puede ser denunciada directamente ante la sede principal o el departamento de quejas regional correspondiente.</p>
          <p class="content-text">Número de contacto en Telegram</p>
          <p class="content-text">(Telegram):<a href="https://t.me/Club_Numero_Uno_vip" target="_blank" class="telegram-link">https://t.me/Club_Numero_Uno_vip</a></p>
        </div>

        <!-- 图片展示区域 -->
        <div class="photo-gallery">
          <div class="photo-row">
            <div class="photo-item">
              <img src="/img/mine/photo_2025-06-29_10-52-17.jpg" alt="Photo 1" />
            </div>
            <div class="photo-item">
              <img src="/img/mine/photo_2025-06-29_10-52-21.jpg" alt="Photo 2" />
            </div>
          </div>
          <div class="photo-row">
            <div class="photo-item">
              <img src="/img/mine/photo_2025-06-29_10-52-23.jpg" alt="Photo 3" />
            </div>
            <div class="photo-item">
              <img src="/img/mine/photo_2025-06-29_10-52-25.jpg" alt="Photo 4" />
            </div>
          </div>
          <div class="photo-row">
            <div class="photo-item">
              <img src="/img/mine/photo_2025-06-29_10-52-27.jpg" alt="Photo 5" />
            </div>
            <div class="photo-item">
              <img src="/img/mine/photo_2025-06-29_10-52-29.jpg" alt="Photo 6" />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      companyInfo: {}
    };
  },
  methods: {
    back(){
      return window.history.back();
    },
    getCompanyInfo(){
      this.$http({
        method: 'get',
        url: 'user_get_personalreport'
      }).then(res=>{
        if(res.code === 200){
          this.companyInfo = res.data;
        }else if(res.code ===401){
          this.$toast(res.msg);
        }
      })
    }
  },
  created() {
    if(!localStorage.getItem('token')){
      this.$router.push({path:'/Login'})
    }else {
      this.getCompanyInfo();
    }
  }
};
</script>

<style lang='less' scoped>
@import "../../assets/css/base.css";

.container .header{
  background: linear-gradient(270deg, #e6c3a1, #7e5678);
  padding-bottom: 30px;
}

.company-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px 30px;
  text-align: center;
}

.company-logo {
  margin-bottom: 20px;
}

.logo-img {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  border: 3px solid rgba(255, 255, 255, 0.3);
}

.company-name {
  font-size: 32px;
  color: #fff;
  margin: 10px 0;
  font-weight: bold;
}

.company-slogan {
  font-size: 24px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

.content {
  flex: 1;
  background: #f2f2f5;
  padding: 20px;
  margin-top: -30px;
  border-radius: 20px 20px 0 0;
}

.info-card {
  background: #fff;
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 28px;
  color: #333;
  margin: 0 0 20px 0;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 10px;
}

.card-title .van-icon {
  color: #f487e0;
  font-size: 30px;
}

.company-description {
  font-size: 16px;
  color: #666;
  line-height: 1.8;
  margin: 0;
  text-align: left;
}

.section-title {
  margin-bottom: 8px !important;
  margin-top: 0 !important;
  font-size: 18px;
  color: #333;
  font-weight: 600;
  padding-left: 2em;
}

.content-text {
  margin-bottom: 6px;
  text-align: left;
  word-spacing: normal;
  letter-spacing: normal;
  padding-left: 2em;
}

.company-description p {
  margin-bottom: 6px;
  text-align: left;
  word-spacing: normal;
  letter-spacing: normal;
}

.telegram-link {
  color: #0088cc;
  text-decoration: none;
  word-break: break-all;
}

.telegram-link:hover {
  text-decoration: underline;
}

/* 图片展示样式 */
.photo-gallery {
  margin-top: 20px;
  padding: 0 10px;
}

.photo-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  gap: 10px;
}

.photo-item {
  flex: 1;
  text-align: center;
}

.photo-item img {
  width: 100%;
  height: auto;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  aspect-ratio: 4/3;
  object-fit: cover;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .company-name {
    font-size: 28px;
  }

  .company-slogan {
    font-size: 22px;
  }

  .card-title {
    font-size: 24px;
  }

  .company-description {
    font-size: 14px;
  }

  .section-title {
    font-size: 16px;
  }

  .company-description p {
    margin-bottom: 5px;
  }
}
</style>
