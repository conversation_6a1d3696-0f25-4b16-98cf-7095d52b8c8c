<template>
	<div class="mine page">
		<div class="page-bg"></div>
		<div class="wrapper">
			<van-pull-refresh :pulling-text="$t('reservation.pull_refresh_pulling')"
			:loosing-text="$t('reservation.pull_refresh_loosing')"
			:loading-text="$t('reservation.pull_refresh_loading')"
			:success-text="$t('reservation.pull_refresh_success')" v-model="isLoading" @refresh="onRefresh">
				<div class="header">
					<van-nav-bar class="nav-bar">
						<template #right>
							<van-icon name="setting-o" @click="showSetting()" color="#fff" />
						</template>
					</van-nav-bar>
					<div class="user-wrapper" @click="doLogin()">
						<div class="user-avatar">
							<van-image round class="user_img" :src="this.userInfo.header_img || 'img/mine/default-avatar.svg'">
								<template v-slot:loading>
									<van-loading type="spinner"/>
								</template>
								<template v-slot:error>
									<img src="img/mine/default-avatar.svg" class="user_img" style="border-radius: 50%;" />
								</template>
							</van-image>
						</div>
						<div class="login-content">
							<p class="user-name">{{this.userInfo.name || this.userInfo.username || '用户'}}</p>
							<p class="user-id">{{this.userInfo.username || '0123456789'}}</p>
						</div>
					</div>
				</div>
				<div class="content">
					<div class="finance">
						<div class="finance-item" @click="toService()">
							<van-icon class="icon" style="" name="peer-pay" />
							<span class="text">{{$t("my.recharge")}}</span>
						</div>
						<div class="line"></div>
						<div class="finance-item" @click="doWithdrawal()">
							<van-icon class="icon" name="idcard" />
							<span class="text">{{$t("my.withdraw")}}</span>
						</div>
					</div>
					<div v-if="this.userInfo.money" class="wallet">
						<div class="part-1 van-hairline--bottom">
							<p class="flex-1 font-28 font-primary-color">{{$t("my.my_balance")}}</p>
							<!-- <span class="font-28 font-gray">{{$t("my.detail")}}</span>
							<van-icon class="font-gray" style="font-size: 28px" name="arrow" /> -->
						</div>
						<div class="part-2">
							<p class="balance van-ellipsis">{{formatMoney(this.userInfo.money)}}</p>
							<span class="font-28 font-gray">{{$t('my.balance')}}（HKD）</span>
							<div class="refresh-btn" @click="refresh()"><van-icon name="replay" /></div>
						</div>
						<!-- 信用积分已隐藏 -->

					</div>
					<div :style="{ marginTop: menu_top +'px'}" class="menu">
						<div class="menu-item" @click="$router.push({path:'/Infomation'});">
							<van-image class="menu-item-icon" src="img/mine/user.svg">
								<template v-slot:loading>
									<van-loading type="spinner" />
								</template>
							</van-image>
							<span class="menu-item-label">{{$t("my.personal_center")}}</span>
						</div>
						<!-- <div class="menu-item" @click="exit()"> -->
							<div class="menu-item" @click="$router.push({path:'/Account'});">
							<van-image class="menu-item-icon" src="img/mine/mingxi.svg">
								<template v-slot:loading>
									<van-loading type="spinner" />
								</template>
							</van-image>
							<span class="menu-item-label">{{$t("my.exchange_record")}}</span>
						</div>
						<div class="menu-item" @click="$router.push({path:'/GameRecord'});">
							<van-image class="menu-item-icon" src="img/mine/youxi.svg">
								<template v-slot:loading>
									<van-loading type="spinner" />
								</template>
							</van-image>
							<span class="menu-item-label">{{$t("my.task_record")}}</span>
						</div>
						<div class="menu-item" @click="toService()">
							<van-image class="menu-item-icon" src="img/mine/kefu_1.svg">
								<template v-slot:loading>
									<van-loading type="spinner" />
								</template>
							</van-image>
							<span class="menu-item-label">{{$t("my.online_service")}}</span>
						</div>
						<div class="menu-item menu-item-empty">
							<!-- 空白占位项，保持布局平衡 -->
						</div>
					</div>
				</div>
			</van-pull-refresh>
		</div>
	</div>

</template>

<script>
	export default {
		data() {
			return {
				userInfo: {},
				menu_top: 40,
				isLoading: false,
			};
		},
		methods: {
			// 判断是否为VIP0用户
			isVip0User() {
				// 如果用户信息不存在，返回true（隐藏VIP信息）
				if (!this.userInfo || !this.userInfo.vip) {
					return true;
				}
				// 如果VIP等级为0或者字符串'0'，则为VIP0用户
				return this.userInfo.vip === 0 || this.userInfo.vip === '0' || this.userInfo.vip === 'VIP0';
			},
			formatDate(dateString) {
				if (!dateString) return '--';
				const date = new Date(dateString);
				if (isNaN(date.getTime())) return '--';
				return date.getFullYear() + '-' +
					String(date.getMonth() + 1).padStart(2, '0') + '-' +
					String(date.getDate()).padStart(2, '0');
			},
			refresh() {
				this.isLoading = true;
				setTimeout(() => {
					this.isLoading = false;
					if (localStorage.getItem('token')) {
						this.$toast(this.$t("reservation.refresh"));
					} else {
						this.$router.push({
							path: '/Auth'
						})
					}
				}, 500);
			},
			exit() {
				this.$toast(this.$t("my.finish_task"));
			},
			showSetting() {
				if (localStorage.getItem('token')) {
					this.$router.push({
						path: '/Setting'
					})
				} else {
					this.$router.push({
						path: '/Auth'
					})
				}
			},

			onRefresh() {
				setTimeout(() => {
					this.isLoading = false;
					if (localStorage.getItem('token')) {
						this.getUserInfo();
						this.$toast(this.$t("reservation.refresh"));
					} else {
						this.$router.push({
							path: '/Auth'
						})
					}
				}, 500);
			},
			doLogin() {
				if (localStorage.getItem('token')) {
					this.$router.push({
						path: '/Infomation'
					});
				} else {
					this.$router.push({
						path: '/Auth'
					})
				}
			},
			doPay() {
				this.$router.push({
					name: 'Recharge',
					params: {
						'balance': this.userInfo.money
					}
				})
			},
			doWithdrawal() {
				this.$http({
					method: 'get',
					url: 'user_get_bank'
				}).then(res => {
					if (res.data.is_bank) {
						this.$router.push("withdraw");
					} else {
						this.$router.push("Setbank");
						this.$toast.fail(this.$t("setting.set_bank"));
					}
				})
			},
			toService() {
				if (this.$store.getters.getBaseInfo.iskefu == 1) {
					this.$router.push("ServiceOnline");
				} else {
					this.$toast.fail(this.$t("setting.forbid"));
				}
			},

			// 格式化金额为香港格式
			formatMoney(amount) {
				if (!amount && amount !== 0) return '0';
				const num = parseFloat(amount);
				if (isNaN(num)) return '0';

				// 如果是整数，不显示小数点
				if (num % 1 === 0) {
					return num.toLocaleString('en-HK');
				} else {
					return num.toLocaleString('en-HK', {
						minimumFractionDigits: 2,
						maximumFractionDigits: 2
					});
				}
			},
			getUserInfo() {
				this.$http({
					method: 'get',
					url: 'user_info'
				}).then(res => {
					if (res.code === 200) {
						this.userInfo = res.data;
						this.menu_top = 15;
						if (this.userInfo.status !== 1) {
							this.$toast(this.$t("video.account_out"));
							localStorage.clear()
							this.$router.push({
								path: '/Auth'
							})
						}
					} else if (res.code === 401) {
						this.$toast(res.msg);
					}
				})
			},
		},
		created() {
			if (localStorage.getItem('token')) {
				this.getUserInfo();
			} else {
				this.userInfo.username = this.$t("setting.log_reg");
				this.userInfo.name = "";
				this.userInfo.id = "";
				this.userInfo.vip_open_time = null;
				this.userInfo.vip_remaining_days = 0;
				this.userInfo.header_img = "";
			}
		}
	};
</script>

<style scoped>
	.page {
		position: absolute !important;
		top: 0;
		left: 0;
		right: 0;
		background-color: #f2f2f5;
	}

	.mine {
		position: relative;
		bottom: 10px;
		background: #f2f2f5;
	}

	.mine .wrapper {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 110px; /* 为底部导航栏预留空间 */
		overflow-y: auto;
		-webkit-overflow-scrolling: touch;
	}

	.nav-bar {
		background: linear-gradient(90deg, #f487e0,#988fba);
	}

	.mine .header {
		background: linear-gradient(90deg, #f487e0,#988fba);
		padding-bottom: 100px;
	}

	::v-deep .van-nav-bar__content {
		height: 100px;
	}

	::v-deep .van-hairline--bottom::after {
		border-bottom-width: 0px;
	}

	.mine .header .van-nav-bar .van-icon {
		font-size: 45px;
	}

	.mine .header .user-wrapper {
		display: flex;
		align-items: center;
		margin: 0px 40px 0px 40px;
	}

	.mine .user-avatar {
		position: relative;
	}

	.mine .user_img {
		height: 80px;
		width: 80px;
	}



	::v-deep .van-loading__spinner {
		height: 50px;
		width: 50px;
	}

	::v-deep .van-image__error-icon {
		font-size: 70px;
	}

	.mine .header .user-wrapper .login-content {
		flex: 1;
		margin-left: 30px;
	}

	.mine .header .user-wrapper .login-content .user-name {
		font-size: 32px;
		color: #fff;
		font-weight: bold;
		margin-bottom: 8px;
		line-height: 1.2;
	}

	.mine .header .user-wrapper .login-content .user-id {
		font-size: 28px;
		color: rgba(255, 255, 255, 0.8);
		line-height: 1.2;
		margin: 0;
	}

	.mine .header .user-wrapper .login-content .login-btn {
		display: inline-block;
		font-size: 40px;
		line-height: 0px;
		color: #fff;
	}

	.mine .header .user-wrapper .login-content .login-label {
		margin-top: -13px;
		font-size: 28px;
		color: hsla(0, 0%, 100%, .6);
	}

	.mine .header .user-wrapper .login-content .vip-info {
		margin-top: 10px;
	}

	.mine .header .user-wrapper .login-content .vip-info .vip-detail {
		margin: 5px 0;
		font-size: 24px;
		color: hsla(0, 0%, 100%, .8);
		line-height: 1.2;
	}

	.mine .page-bg {
		height: 500px;
		background: linear-gradient(90deg, #f487e0,#988fba);
	}

	.mine .content {
		position: relative;
		padding: 10px 30px 50px; /* 增加底部内边距 */
		min-height: 500px;
		background-color: #f2f2f5;
	}

	::v-deep .van-pull-refresh__track .van-pull-refresh__head * {
		color: #ffffff;
		font-size: 35px;
	}

	.mine .wrapper .content .finance {
		position: absolute;
		display: flex;
		align-items: center;
		top: -55px;
		left: 30px;
		right: 30px;
		height: 120px;
		background-color: #fff;
		border-radius: 15px;
		box-shadow: 0 1.5px 1px 0 #e4e4e7;
	}

	.mine .wrapper .content .finance .line {
		width: 3px;
		height: 40px;
		background-color: #ccc;
	}

	.mine .wrapper .content .finance .finance-item {
		flex: 1;
		display: flex;
		align-items: center;
		justify-content: center;
		height: 100%;
	}

	.mine .wrapper .content .finance .finance-item .text {
		margin-left: 30px;
		font-size: 30px;
		color: #000;
		font-weight: 500;
	}

	.mine .wrapper .content .finance .finance-item .icon {
		font-size: 50px;
	}

	.mine .wrapper .content .menu {
		display: flex;
		align-items: center;
		flex-wrap: wrap;
		background-color: #fff;
		border-radius: 15px;
		box-shadow: 0 1.5px 1px 0 #e4e4e7;
	}

	.mine .wrapper .content .menu .menu-item {
		display: flex;
		align-items: center;
		justify-content: flex-start;
		width: 50%;
		height: 130px;
	}

	.mine .wrapper .content .menu .menu-item-empty {
		visibility: hidden;
	}

	.mine .wrapper .content .menu .menu-item .menu-item-label {
		font-size: 30px;
		color: #868686;
		font-weight: 500;
	}

	.mine .wrapper .content .menu .menu-item .menu-item-icon {
		margin: 25px;
		width: 60px;
		height: 60px;
		-o-object-fit: contain;
		object-fit: contain;
	}

	.mine .wrapper .content .wallet {
		margin-top: 80px;
		padding: 0 30px;
		background-color: #fff;
		border-radius: 15px;
		box-shadow: 0 1.5px 1px 0 #e4e4e7;
	}

	.mine .wrapper .content .wallet .part-1 {
		display: flex;
		align-items: center;
		height: 100px;
	}

	.mine .wrapper .content .wallet .font-primary-color {
		color: #000;
	}

	.font-gray {
		color: #868686;
	}

	.mine .wrapper .content .wallet .part-2 {
		display: flex;
		align-items: center;
		height: 150px;
	}

	.mine .wrapper .content .wallet .part-2 .balance {
		flex: 1;
		font-size: 60px;
		color: #f487e0;
		font-weight: 700;
	}

	.mine .wrapper .content .wallet .van-hairline--bottom::after {
		border-bottom-width: 3px;
	}

	.mine .wrapper .content .wallet .part-2 .refresh-btn {
		margin-left: 30px;
		display: flex;
		align-items: center;
		justify-content: center;
		width: 50px;
		height: 50px;
		font-size: 30px;
		border-radius: 50%;
		color: #ffffff;
		background-color: #e6c3a1;
	}
</style>