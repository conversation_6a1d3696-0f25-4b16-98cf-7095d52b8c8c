<template>
	<div class="convention-hall page">
		<van-nav-bar class="nav-bar" :title="$t('my.exchange_record')" >
		<template #left>
          <van-icon name="arrow-left" color="#fff" @click="back()"/>
        </template>
	</van-nav-bar>
		<div class="convention-item">
			<!-- 只显示兑换积分记录 -->
			<van-empty v-if="list.length === 0" :description="$t('withdraw.empty_data')" />
			<div v-else class="item_list" v-for="(vv,key1) in list" :key="key1">
				<div class="topInfo">
				<span v-if="vv.status === 2" style="color: #07c160">{{vv.status_text}}</span>
				<span v-else-if="vv.status === 4" style="color: #07c160">{{vv.status_text}}</span>
				<span v-else >{{vv.status_text}}</span>
				<span>{{$t("my.points")}}：-{{vv.money}}</span>
				</div>
				<div class="time">
				<span>{{$t("withdraw.submit_time")}}：{{vv.create_time}}</span>
				</div>
				<div class="time">
				<span>{{$t("withdraw.check_time")}}：{{vv.update_time}}</span>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
	data() {
		return {
			list:[] // 只保留兑换积分记录
		};
	},
	methods: {
		back(){
		return window.history.back();
		},
		getUserWithdrawList(){
		this.$http({
			method: 'get',
			url: 'user_get_withdraw_list'
		}).then(res=>{
			if(res.code === 200){
			this.list = res.data;
			}else if(res.code ===401){
			// this.$toast(res.msg);
			}
		})
		}
	},
	created() {
		if(!localStorage.getItem('token')){
		this.$router.push({path:'/Login'})
		}else {
		this.getUserWithdrawList(); // 只获取兑换积分记录
		}
	}
};
</script>

<style lang="less" scoped>
.page {
	position: absolute !important;
	top: 0;
	left: 0;
	right: 0;
	background-color: #f2f2f5;
}
.nav-bar {
	background: linear-gradient(90deg, #f487e0,#988fba);
	height: 100px;
}
.van-nav-bar {
	line-height: 50px;
}
::v-deep .van-nav-bar__title {
	max-width: 60%;
	margin: 0 auto;
	color: #ffffff;
	font-size: 35px;
}
::v-deep .van-nav-bar__content {
	height: 100px;
}
.item_list .topInfo span{
  flex: 1;
  font-size: 35px;
  font-weight: 700;
  color: #ff253f;
}
.item_list .time span{
  flex: 1;
  font-size: 25px;
  font-weight: 500;
  color: #000;
}

.item_list .topInfo span:last-child{
  float: right;
}
.item_list .desc span{
  font-size: 25px;
  font-weight: 700;
  color: #9b9b9b;
}
.van-sidebar {
	width: 180px;
}
// /deep/ .van-col{
	// padding: 30px 0px;
// }
.convention-hall {
	display: flex;
	flex-direction: column;
	bottom: 20px;
	background: #f2f2f5;
	height: max-content;
}
::v-deep .van-tab {
	font-size: 30px;
	line-height: 100px;
	font-weight: bold;
}
::v-deep .van-tabs__line {
	background-color: #f487e0;
}
::v-deep .van-tabs--line .van-tabs__wrap {
	height: 100px;
}
::v-deep .van-tabs__wrap--scrollable .van-tab {
	padding: 0 23px;
}
.card {
	background-color: #8a637d;
	padding: 0.625rem;
	width: 95%;
	color: white;
	font-size: 0.8125rem;
	margin: 0.625rem auto;
	border-radius: 0.375rem;
}
::v-deep .van-row--flex {
	height: 80px;
	line-height: 80px;
}

/deep/ .van-cell{
	padding: 30px 22px;
	font-size: 30px;
	line-height:30px;
}
.rig-box {
	width: 95%;
	margin: 0.625rem auto;
}
.rig-title {
	color: #0bdab0;
	font-size: 1.125rem;
}
.rig-content {
	font-size: 20px;
	// margin-top: 10px;
}
.address {
	width: 94%;
	margin: 0 auto;
	margin-bottom: 110px;
}
</style>
