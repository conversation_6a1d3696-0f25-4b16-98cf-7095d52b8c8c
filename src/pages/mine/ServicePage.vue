<template>
  <div class="container page">
    <div class="header">
      <van-nav-bar :title="$t('my.online_service')" class="nav-bar">
        <template #left>
          <van-icon name="arrow-left" color="#fff" @click="back()"/>
        </template>
      </van-nav-bar>
    </div>
    <div class="ifrmae_page">
      <iframe width="100%" height="100%"  frameborder="0"  id="iframe_web"   :src="getSecureUrl(this.$store.getters.getBaseInfo.kefu)"  ></iframe>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
    };
  },
  methods: {
    back(){
      return window.history.back();
    },
    // 确保URL使用HTTPS，避免混合内容警告
    getSecureUrl(url) {
      if (!url) {
        return 'https://hao.360.com/'; // 默认安全链接
      }

      // 如果是HTTP链接，转换为HTTPS
      if (url.startsWith('http://')) {
        return url.replace('http://', 'https://');
      }

      // 如果已经是HTTPS或相对路径，直接返回
      if (url.startsWith('https://') || url.startsWith('//')) {
        return url;
      }

      // 如果是相对路径，添加HTTPS前缀
      return `https://${url}`;
    }
  },
  created() {

  },
  mounted(){
    /**
     * iframe-宽高自适应显示
     */
    const oIframe = document.getElementById('iframe_web');
    const deviceHeight = document.documentElement.clientHeight;
    oIframe.style.height = (Number(deviceHeight)-65) + 'px'; //数字是页面布局高度差
  }
};
</script>

<style lang='less' scoped>
@import "../../assets/css/base.css";
</style>
