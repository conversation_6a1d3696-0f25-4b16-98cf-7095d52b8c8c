<template>
  <div class="container page">
    <van-nav-bar :title="$t('my.task_record')" class="nav-bar">
      <template #left>
        <van-icon name="arrow-left" color="#fff" @click="back()"/>
      </template>
    </van-nav-bar>
    <div class="main">
      <van-pull-refresh :pulling-text="$t('reservation.pull_refresh_pulling')"
			:loosing-text="$t('reservation.pull_refresh_loosing')"
			:loading-text="$t('reservation.pull_refresh_loading')"
			:success-text="$t('reservation.pull_refresh_success')" v-model="isLoading" @refresh="onRefresh">
        <!-- 顶部统计卡片 -->
        <div class="stats-card">
          <div class="stats-header">
            <span class="stats-title">第 {{currentPeriod}} 编码</span>
            <van-icon name="arrow-up" class="expand-icon" />
          </div>
          <div class="stats-subtitle">PK獲勝主播：蝶，蝶,運理技</div>
        </div>

        <!-- 表格头部 -->
        <div class="table-header">
          <div class="header-cell left">編碼</div>
          <div class="header-cell right">正確打賞</div>
        </div>

        <!-- 打赏记录列表 -->
        <van-empty v-if="rewardList.length === 0" :description="$t('withdraw.empty_data')" />
        <div v-else class="reward-list">
          <div class="reward-item" v-for="(item, index) in rewardList" :key="index">
            <div class="reward-code">{{item.code}}</div>
            <div class="reward-target">{{item.target}}</div>
          </div>
        </div>
      </van-pull-refresh>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      currentPeriod: '20250828525249',
      rewardList: [
        { code: '20250828525249', target: '蝶, 蝶,運理技' },
        { code: '20250828525248', target: '蝶, 蝶,奇相思' },
        { code: '20250828525247', target: '蝶, 蝶,運理技' },
        { code: '20250828525246', target: '蝶, 蝶,運理技' },
        { code: '20250828525245', target: '蝶, 蝶,奇相思' },
        { code: '20250828525244', target: '蝶, 蝶,奇相思' },
        { code: '20250828525243', target: '蝶, 蝶,運理技' },
        { code: '20250828525242', target: '蝶, 蝶,運理技' }
      ],
      isLoading: false
    };
  },
  methods: {
    back(){
      return window.history.back();
    },

    onRefresh() {
      setTimeout(() => {
        this.$toast(this.$t("reservation.refresh"));
        this.isLoading = false;
      }, 500);
    }

  },
  created() {
    if(!localStorage.getItem('token')){
      this.$router.push({path:'/Auth'})
    }
  }
};
</script>

<style lang='less' scoped>
@import "../../assets/css/base.css";
::v-deep .van-pull-refresh__track .van-pull-refresh__head *{
  color: #000000;
  font-size: 35px;
}

::v-deep .van-loading__text {
  color: #000000;
  font-size: 35px;
}
.container .main{
  position: relative;
  overflow: auto;
  background-color: #f2f2f5;
  height: 100%;
  padding: 0 10px;
}
/* 统计卡片样式 */
.stats-card {
  background: linear-gradient(135deg, #f487e0, #e6c3a1);
  margin: 15px;
  padding: 20px;
  border-radius: 15px;
  color: white;
  box-shadow: 0 4px 12px rgba(244, 135, 224, 0.3);
}

.stats-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

.stats-title {
  font-size: 18px;
  font-weight: bold;
}

.expand-icon {
  font-size: 16px;
}

.stats-subtitle {
  font-size: 14px;
  opacity: 0.9;
}

/* 表格头部样式 */
.table-header {
  display: flex;
  background: #f487e0;
  margin: 0 15px;
  border-radius: 8px 8px 0 0;
  overflow: hidden;
}

.header-cell {
  flex: 1;
  padding: 15px;
  text-align: center;
  color: white;
  font-weight: bold;
  font-size: 16px;
}

.header-cell.left {
  background: #f487e0;
}

.header-cell.right {
  background: #f487e0;
}

/* 打赏记录列表样式 */
.reward-list {
  background: white;
  margin: 0 15px;
  border-radius: 0 0 8px 8px;
  overflow: hidden;
}

.reward-item {
  display: flex;
  border-bottom: 1px solid #f0f0f0;
}

.reward-item:last-child {
  border-bottom: none;
}

.reward-code {
  flex: 1;
  padding: 15px;
  text-align: center;
  font-size: 14px;
  color: #333;
}

.reward-target {
  flex: 1;
  padding: 15px;
  text-align: center;
  font-size: 14px;
  color: #333;
}
</style>
