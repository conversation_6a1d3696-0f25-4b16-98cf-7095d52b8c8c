<template>
  <div class="container page">
    <div class="header">
      <van-nav-bar :title="$t('my.sys_notice')" class="nav-bar">
        <template #left>
          <van-icon name="arrow-left" color="#fff" @click="back()"/>
        </template>
      </van-nav-bar>
      <div class="company-info">
        <div class="company-logo">
          <van-image src="/img/mine/gonggao.svg" class="logo-img">
            <template v-slot:loading>
              <van-loading type="spinner" />
            </template>
          </van-image>
        </div>
        <h2 class="company-name">{{ $t('my.club_info') }}</h2>
        <p class="company-slogan">{{ $t('my.club_information') }}</p>
      </div>
    </div>

    <div class="content">
      <!-- 俱乐部信息 -->
      <div class="info-card">
        <h3 class="card-title">
          <van-icon name="info-o" />
          {{ $t('my.club_details') }}
        </h3>
        <div class="notice-description">
          <p class="section-title">🏆 Club Número Uno – Información General</p>
          <p class="content-text">Club Número Uno opera bajo un sistema de membresía VIP por niveles, ofreciendo un entorno seguro, profesional y confidencial para todos sus clientes. Todas las operaciones están respaldadas por certificaciones legales y contratos oficiales.</p>

          <p class="section-title">📜 Sistema de Protección del Cliente</p>
          <p class="content-text"><strong>Protección mediante Contrato</strong></p>
          <p class="content-text">Todos los clientes están protegidos mediante acuerdos contractuales formales, garantizando la seguridad de sus derechos, intereses y privacidad.</p>
          <p class="content-text"><strong>Contenido Certificado en la APP</strong></p>
          <p class="content-text">Todos los modos de operación y contenido dentro de la APP del Club Número Uno han sido certificados y legalizados, garantizando la protección total de los derechos e intereses de los usuarios.</p>

          <p class="section-title">🎁 Beneficios y Derechos del Cliente</p>
          <p class="content-text"><strong>Recompensas y Políticas de Incentivo</strong></p>
          <p class="content-text">Todos los beneficios, tales como regalos, bonificaciones, comisiones, reembolsos y puntos de crédito, serán propiedad exclusiva del cliente, conforme a las políticas promocionales de la plataforma.</p>

          <p class="section-title">✅ Certificación y Legalidad</p>
          <p class="content-text"><strong>Certificación Oficial del Club</strong></p>
          <p class="content-text">Acreditado por: Haotian Media Co., Ltd.</p>
          <p class="content-text">Capital registrado: 50 millones de USD</p>
          <p class="content-text">Información registrada y emitida por los Departamentos de Asuntos Jurídicos y Comerciales</p>
          <p class="content-text">Todas las operaciones del club son legales, con contratos firmados y certificados</p>

          <p class="section-title">🌐 Reglas de Servicio Online/Offline</p>
          <p class="content-text"><strong>Atención Oficial Exclusiva</strong></p>
          <p class="content-text">Todos los clientes deben contactar únicamente con el servicio oficial dentro de la APP. Se prohíben operaciones privadas fuera de línea, garantizando así la legalidad y seguridad de los procesos.</p>

          <p class="section-title">🧩 Estructura Organizativa</p>
          <p class="content-text"><strong>Departamentos del Club</strong></p>
          <p class="content-text">Departamento Comercial: Confirmación de operaciones</p>
          <p class="content-text">Departamento de Negocios: Desarrollo y soporte</p>
          <p class="content-text">Atención al Cliente: Servicio personalizado</p>
          <p class="content-text">Cada cliente será atendido por un asesor exclusivo, quien gestionará la coordinación con los departamentos correspondientes para satisfacer sus necesidades de manera rápida y eficiente.</p>

          <p class="section-title">👥 Gestión y Mantenimiento del Cliente</p>
          <p class="content-text"><strong>Canales de Comunicación Protegidos</strong></p>
          <p class="content-text">Para facilitar el acceso a información y asistencia oportuna, el club ha creado grupos de membresía diferenciados:</p>
          <p class="content-text">Grupo VIP General</p>
          <p class="content-text">Grupo VIP Avanzado</p>
          <p class="content-text">A través de estos canales, los clientes podrán conocer las actividades del club en tiempo real y resolver cualquier inconveniente con ayuda del personal designado, todo ello bajo estrictas normas de confidencialidad.</p>
        </div>

        <!-- 新增图片展示区域 -->
        <div class="image-gallery">
          <div class="image-row">
            <img src="/img/mine/photo_2025-06-29_11-04-28.jpg" alt="Club Image" class="gallery-image" />
            <img src="/img/mine/photo_2025-06-29_11-04-30.jpg" alt="Club Image" class="gallery-image" />
          </div>
          <div class="image-row">
            <img src="/img/mine/photo_2025-06-29_11-04-32.jpg" alt="Club Image" class="gallery-image" />
            <img src="/img/mine/photo_2025-06-29_11-04-34.jpg" alt="Club Image" class="gallery-image" />
          </div>
          <div class="image-row">
            <img src="/img/mine/photo_2025-06-29_11-04-36.jpg" alt="Club Image" class="gallery-image" />
            <img src="/img/mine/photo_2025-06-29_11-04-38.jpg" alt="Club Image" class="gallery-image" />
          </div>
          <div class="image-row">
            <img src="/img/mine/photo_2025-06-29_11-04-40.jpg" alt="Club Image" class="gallery-image" />
            <img src="/img/mine/photo_2025-06-29_11-04-42.jpg" alt="Club Image" class="gallery-image" />
          </div>
          <div class="image-row">
            <img src="/img/mine/photo_2025-06-29_11-04-44.jpg" alt="Club Image" class="gallery-image" />
            <img src="/img/mine/photo_2025-06-29_11-04-46.jpg" alt="Club Image" class="gallery-image" />
          </div>
          <div class="image-row">
            <img src="/img/mine/photo_2025-06-29_11-04-49.jpg" alt="Club Image" class="gallery-image" />
            <img src="/img/mine/photo_2025-06-29_11-04-51.jpg" alt="Club Image" class="gallery-image" />
          </div>
          <div class="image-row">
            <img src="/img/mine/photo_2025-06-29_11-04-53.jpg" alt="Club Image" class="gallery-image" />
            <img src="/img/mine/photo_2025-06-29_11-04-55.jpg" alt="Club Image" class="gallery-image" />
          </div>
          <div class="image-row">
            <img src="/img/mine/photo_2025-06-29_11-04-58.jpg" alt="Club Image" class="gallery-image" />
            <img src="/img/mine/photo_2025-06-29_11-05-00.jpg" alt="Club Image" class="gallery-image" />
          </div>
          <div class="image-row">
            <img src="/img/mine/photo_2025-06-29_11-05-02.jpg" alt="Club Image" class="gallery-image" />
            <img src="/img/mine/photo_2025-06-29_11-05-04.jpg" alt="Club Image" class="gallery-image" />
          </div>
          <div class="image-row">
            <img src="/img/mine/photo_2025-06-29_11-05-06.jpg" alt="Club Image" class="gallery-image" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      // 移除了后台请求相关的数据
    };
  },
  methods: {
    back(){
      return window.history.back();
    }
  },
  created() {
    // 移除了后台请求
  }
};
</script>

<style lang='less' scoped>
@import "../../assets/css/base.css";

.container .header{
  background: linear-gradient(270deg, #e6c3a1, #7e5678);
  padding-bottom: 30px;
}

.company-info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 20px 30px;
  text-align: center;
}

.company-logo {
  margin-bottom: 20px;
}

.logo-img {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  border: 3px solid rgba(255, 255, 255, 0.3);
}

.company-name {
  font-size: 32px;
  color: #fff;
  margin: 10px 0;
  font-weight: bold;
}

.company-slogan {
  font-size: 24px;
  color: rgba(255, 255, 255, 0.8);
  margin: 0;
}

.content {
  flex: 1;
  background: #f2f2f5;
  padding: 20px;
  margin-top: -30px;
  border-radius: 20px 20px 0 0;
}

.info-card {
  background: #fff;
  border-radius: 15px;
  padding: 25px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 28px;
  color: #333;
  margin: 0 0 20px 0;
  font-weight: bold;
  display: flex;
  align-items: center;
  gap: 10px;
}

.card-title .van-icon {
  color: #f487e0;
  font-size: 30px;
}

.notice-description {
  font-size: 16px;
  color: #666;
  line-height: 1.8;
  margin: 0;
  text-align: left;
}

.section-title {
  margin-bottom: 8px !important;
  margin-top: 0 !important;
  font-size: 18px;
  color: #333;
  font-weight: 600;
  padding-left: 2em;
}

.content-text {
  margin-bottom: 6px;
  text-align: left;
  word-spacing: normal;
  letter-spacing: normal;
  padding-left: 2em;
}

.notice-description p {
  margin-bottom: 6px;
  text-align: left;
  word-spacing: normal;
  letter-spacing: normal;
}

.telegram-link {
  color: #0088cc;
  text-decoration: none;
  word-break: break-all;
}

.telegram-link:hover {
  text-decoration: underline;
}

.image-gallery {
  margin-top: 20px;
  padding: 0 10px;
}

.image-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  gap: 10px;
}

.gallery-image {
  width: calc(50% - 5px);
  height: auto;
  border-radius: 8px;
  object-fit: cover;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.image-row:last-child {
  justify-content: flex-start;
}

.image-row:last-child .gallery-image {
  width: calc(50% - 5px);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .company-name {
    font-size: 28px;
  }

  .company-slogan {
    font-size: 22px;
  }

  .card-title {
    font-size: 24px;
  }

  .notice-description {
    font-size: 14px;
  }

  .section-title {
    font-size: 16px;
  }

  .notice-description p {
    margin-bottom: 5px;
  }

  .image-gallery {
    padding: 0 5px;
  }

  .image-row {
    gap: 5px;
    margin-bottom: 8px;
  }

  .gallery-image {
    width: calc(50% - 2.5px);
    border-radius: 6px;
  }
}
</style>
