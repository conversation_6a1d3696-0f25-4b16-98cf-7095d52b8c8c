<template>
  <div class="movie-hall page">
    <van-nav-bar
        class="nav-bar"
        :title="$t('video.video')"
    />
    <van-tabs v-model="active" animated swipeable  @change="OnChange">
      <van-tab v-for="(v,key) in videolitem" :key="key" :title="v.name" :name="v.key" ></van-tab>
    </van-tabs>
    <swiper class="video_swiper" ref="swiper" :options="videoSwiperOption" @slideChange="itemChange">
      <swiper-slide v-for="(v,key) in videolitem" :key="key">
        <div class="movie-list-tab">
          <van-pull-refresh :pulling-text="$t('reservation.pull_refresh_pulling')"
			:loosing-text="$t('reservation.pull_refresh_loosing')"
			:loading-text="$t('reservation.pull_refresh_loading')"
			:success-text="$t('reservation.pull_refresh_success')" v-model="isLoading" @refresh="onRefresh">
            <div class="hot-recommend-div">
                <van-list
                    v-model="loading"
                    :finished="finished"
                    :immediate-check="true"
                    :finished-text="$t('video.no_more')"
                    @load="onLoad"
                >
                  <div class="list-item">
                    <div class="movie-list-item" v-for="(v,key) in videolist" :key="key" @click="toPlayVideo(v.id)">
                      <van-image class="cover_img"  round :src="common.getImageUrl(v.vod_pic)">
                        <template v-slot:loading>
                          <van-loading type="circular"/>
                        </template>
                      </van-image>
                      <div class="movie-list-item-bottom">
                        <div class="movie-time-div">
                          <span>{{v.vod_name}}</span>
                          <span>{{$t("video.play")}}:{{v.count}}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </van-list>

            </div>
          </van-pull-refresh>
        </div>
      </swiper-slide>
    </swiper>
  </div>
</template>

<script>
import { Toast } from 'vant';
export default {
  data() {
    return {
      active: 0,
      isLoading: false,
      count:0,
      loading: false,
      finished: false,
      refreshing: false,
      videolitem: [],
      videolist: [],
      number:0,
      page:1, // 修复：页码从1开始
      isRequesting: false, // 添加请求锁，防止重复请求
      videoSwiperOption: {
        slidesPerView: 'auto',
        spaceBetween: 0,
        slidesPerGroup : 1,
      }
    };
  },
  methods: {
    getVideoClass(){
      console.log('开始获取视频分类');
      this.$http({
        method: 'get',
        url: 'video_class'
      }).then(res=>{
        console.log('视频分类获取成功:', res.data);
        this.videolitem = res.data;

        // 分类加载完成后，设置第一个分类为默认选中
        if (res.data && res.data.length > 0) {
          console.log('完整分类数据:', res.data);
          console.log('第一个分类:', res.data[0]);

          // 尝试多种可能的ID字段
          this.active = res.data[0].key || res.data[0].id || res.data[0].value || 0;
          console.log('设置默认分类ID:', this.active, '分类数据:', res.data[0]);

          // 如果active仍然是0，说明数据结构有问题
          if (this.active === 0) {
            console.warn('警告：分类ID为0，可能数据结构有问题');
          }

          // 初始化第一个分类的数据
          this.OnChange();
        }
      }).catch(err => {
        console.error('获取视频分类失败:', err);
      })
    },
    toPlayVideo(id){
      if(!localStorage.getItem('token')){
        this.$router.push({path:'/Register'})
      }else {
        this.$router.push({path:'/PlayVideo?id='+id})
      }

    },
    itemChange(){
      const swiperIndex = this.$refs.swiper.swiper.activeIndex;
      console.log('swiper滑动事件 - 当前索引:', swiperIndex);

      // 根据swiper索引找到对应的分类key
      if (this.videolitem && this.videolitem[swiperIndex]) {
        const categoryData = this.videolitem[swiperIndex];
        const newActive = categoryData.key || categoryData.id || categoryData.value || 0;
        console.log('swiper索引', swiperIndex, '对应分类数据:', categoryData, '分类key:', newActive);

        // 只有当active真正改变时才触发OnChange
        if (this.active !== newActive) {
          this.active = newActive;
          this.OnChange();
        }
      }
    },
    getVideoList(){
      // 防重复请求检查
      if (this.isRequesting) {
        console.log('请求正在进行中，跳过重复请求');
        return;
      }

      console.log('获取视频列表 - 当前页码:', this.page, '分类ID:', this.active);
      this.isRequesting = true;

      this.$http({
        method: 'get',
        data:{id:this.active,page:this.page},
        url: 'video_list'
      }).then(res=>{
        console.log('视频列表响应:', res.data);
        console.log('当前视频数量:', this.videolist.length, '新增视频数量:', res.data.data.length);

        this.videolist = this.videolist.concat(res.data.data);
        this.count = res.data.count;
        this.page++;

        console.log('页码已更新为:', this.page, '总视频数:', this.videolist.length, '总数量:', this.count);

        // 延迟设置loading状态，确保van-list能正确处理
        setTimeout(() => {
          this.loading = false;
          this.isRequesting = false; // 释放请求锁
          if (this.videolist.length >= this.count) {
            this.finished = true;
            console.log('所有数据已加载完成');
          }
        }, 100);
      }).catch(err => {
        console.error('获取视频列表失败:', err);
        setTimeout(() => {
          this.loading = false;
          this.isRequesting = false; // 释放请求锁
        }, 100);
      })
    },
    onLoad() {
      console.log('onLoad 被调用 - 当前loading状态:', this.loading, 'finished状态:', this.finished);

      // 如果已完成加载所有数据，则不执行
      if (this.finished) {
        console.log('所有数据已加载完成，跳过请求');
        return;
      }

      this.getVideoList();
    },
     OnChange(){
      console.log('OnChange 被调用 - 当前分类ID:', this.active);

      // 同步swiper位置（如果是通过van-tabs触发的）
      if (this.$refs.swiper && this.$refs.swiper.swiper && this.videolitem) {
        // 根据active(key)找到对应的数组索引
        const targetIndex = this.videolitem.findIndex(item => {
          const itemKey = item.key || item.id || item.value || 0;
          return itemKey === this.active;
        });
        const currentIndex = this.$refs.swiper.swiper.activeIndex;

        console.log('查找分类索引 - 目标key:', this.active, '找到索引:', targetIndex, '当前swiper索引:', currentIndex);

        if (targetIndex !== -1 && currentIndex !== targetIndex) {
          console.log('同步swiper位置: 索引', currentIndex, '->', targetIndex, '(分类key:', this.active, ')');
          this.$refs.swiper.swiper.slideTo(targetIndex);
        }
      }

      console.log('重置数据状态');
      this.videolist = [];
      this.number = 0;
      this.page = 1; // 修复：页码从1开始
      this.count = 0;
      this.finished = false;
      this.loading = false;
      this.isRequesting = false; // 重置请求锁

      // 通过nextTick确保DOM更新后再手动触发一次onLoad
      this.$nextTick(() => {
        console.log('OnChange 完成，手动触发第一次加载');
        // 手动触发第一次加载，因为van-list可能不会自动检查
        this.onLoad();
      });
    },
    onRefresh() {
      console.log('下拉刷新开始');
      // 重置分页和数据
      this.videolist = [];
      this.page = 1;
      this.count = 0;
      this.finished = false;
      this.loading = false;
      this.isRequesting = false; // 重置请求锁

      // 通过nextTick让van-list重新检查并触发onLoad
      this.$nextTick(() => {
        console.log('刷新完成，手动触发加载');
        // 手动触发一次onLoad，因为van-list可能不会自动检查
        this.onLoad();
      });

      setTimeout(() => {
        this.isLoading = false;
        Toast(this.$t("reservation.refresh"));
      }, 500);
    },
  },
  created() {
    console.log('页面创建 - 开始初始化');
    this.getVideoClass();//获取视频类目，完成后会自动调用OnChange
  }
};
</script>

<style lang='less' scoped>
.page{
  position: absolute!important;
  top: 0;
  left: 0;
  right: 0;
  background-color: #f2f2f5;
}
.nav-bar{
  background: linear-gradient(
      90deg,#f487e0,#988fba);
  height: 100px;

}
.van-nav-bar {
  line-height: 50px;
}
::v-deep .van-tab {
	font-size: 30px;
	line-height: 100px;
	}

::v-deep .van-nav-bar__title {
  max-width: 60%;
  margin: 0 auto;
  color: #ffffff;
  font-size: 35px;
}
::v-deep .van-nav-bar__content {
  height: 100px;
}

.movie-hall{
  display: flex;
  flex-direction: column;
  bottom: 110px; /* 匹配底部导航栏高度 */
  background: #f2f2f5;
}
::v-deep .van-tabs__nav {
  background: linear-gradient(
      90deg,#f487e0,#988fba);
}
::v-deep .van-tab {
  color: #ffffff;
  font-size: 30px;
}
::v-deep .van-tabs__line {
  bottom: 15px;
  width: 55px;
  height: 7px;
  border-radius: 0px;
  background-color: #ffffff;
}
::v-deep .van-tabs--line .van-tabs__wrap {
  height: 100px;
}
::v-deep .van-tabs__wrap--scrollable .van-tab {
  padding: 0 23px;
}
::v-deep  .van-hairline--bottom::after {
  border-bottom-width: 0px;
}
.video_swiper {
  width: 100%;
  flex: 1;
  .swiper-slide {
    flex-shrink: 0;
    flex-grow: 0;
    flex-basis: 100%;
    justify-content: center;
    height: 100%;
    position: relative;
    transition-property: transform;
  }
}
.movie-list-tab {
  overflow: auto;
  height: 100%;
}
::v-deep .van-pull-refresh__track .van-pull-refresh__head *{
  color: #000;
  font-size: 35px;
}
.movie-list-tab .hot-recommend-div{
  height: 100%;
  margin: 10px auto;
  display: flex;
  align-items: flex-start;
  justify-content: flex-start;
  flex-wrap: wrap;
  //overflow: auto;
}
.list-item{
  display: flex;
  width: calc(100% - 50px);
  margin: 10px auto;
  align-items: flex-start;
  justify-content: flex-start;
  flex-wrap: wrap;
}
.list-item .movie-list-item:nth-child(odd) {
  margin-right: 20px;
}
.movie-list-item .cover_img{
  border-radius: 20px;
  width:335px;
  height:290px;
}
.movie-list-item{
  margin-bottom: -10px;
}
.list-item .movie-list-item-bottom{
  position: relative;
  width: 335px;
  bottom: 42px;
}
.list-item .movie-list-item-bottom .movie-time-div{
  background-color: rgba(0,0,0,.4);
}
.list-item .movie-list-item-bottom>div {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.list-item .movie-list-item-bottom .movie-time-div .van-count-down {
  color: #fff;
}
.list-item .movie-list-item .movie-time-div span:first-child {
  overflow: hidden;
  white-space: nowrap;
  width: 180px;
  padding-left: 8px;
  font-size: 25px;
}
.list-item .movie-time-div {
  color: #fff;
  border-radius: 0 0 20px 20px;
  height: 35px;
}
</style>
