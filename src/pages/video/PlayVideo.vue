<template>
  <div class="container page">
    <van-nav-bar :title="this.videoInfo.vod_name" class="nav-bar">
      <template #left>
        <van-icon name="arrow-left" color="#fff" @click="back()"/>
      </template>
    </van-nav-bar>
    <div class="movie-video">
      <video id="my-video"  class="video-js">
      </video>
    </div>
    <div class="movie-content">
      <div class="movie-descript">
        <p>{{ this.videoInfo.vod_name}}</p>
        <span>{{this.videoInfo.count}}{{ $t("video.num_play") }}</span>
      </div>

      <div class="movie-body">
        <div class="movie-title">
          <div>
            <span>{{ $t("index.recmonmand") }}</span>
          </div>
        </div>
        <div class="movie-list">
          <div class="movie-play-item" @click="toPlayVideo(v.id)" v-for="(v,key) in moreVideoInfo" :key="key">
            <div>
              <img :src="common.getImageUrl(v.vod_pic)" :alt="v.vod_name">
              <div>
                <div class="van-count-down">{{ v.time }}</div>
              </div>
            </div>
            <div>
              <p>{{ v.vod_name }}</p>
              <span>{{ v.count }}{{ $t("video.num_play") }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import videojs from "video.js";
import "videojs-contrib-hls";
export default {
  data() {
    return {
      nowPlayVideoUrl: "",
      cover:"",
      userInfo:[],
      videoInfo:{},
      moreVideoInfo:{},
      player:null,
      is_play:false,
      times: null,
      vipCheckTimer: null, // VIP状态检查定时器
      is_see:0,
      is_time_limit_reached: false, // 是否已达到时间限制
      total_watch_time: 0 // 总观看时间
    };
  },
  methods: {
    back(){
      this.$router.push({path:'Home'})
    },
    getVideoInfo(){
      
      this.$http({
        method: 'get',
        data:{id:this.$route.query.id},
        url: 'video_get_info'
      }).then(res=>{
        this.videoInfo = res.data;
        this.nowPlayVideoUrl = this.videoInfo.vod_play_url;
        this.cover = this.common.getImageUrl(this.videoInfo.vod_pic);
        let videos = document.getElementById('my-video');
        videos.poster = this.cover;
        this.getVideo();
      })

    },
    toPlayVideo(id){
      if(!localStorage.getItem('token')){
        this.$router.push({path:'/Register'})
      }else {
        this.$router.push({path:'?id='+id})
        location.reload();
      }

    },
    getMoreVideoItem(){
      this.$http({
        method: 'get',
        url: 'video_get_more_item'
      }).then(res=>{
          this.moreVideoInfo = res.data;
      })
    },
    getVideo() {
      // 使用通用函数处理视频URL和类型检测
      const videoUrl = this.common.getVideoUrl(this.nowPlayVideoUrl);
      const videoType = this.common.getVideoType(videoUrl);

      console.log('原始视频URL:', this.nowPlayVideoUrl);
      console.log('处理后视频URL:', videoUrl);
      console.log('检测到的视频类型:', videoType);

      if (!videoUrl) {
        console.error('视频URL为空，无法播放');
        this.$toast('视频地址无效');
        return;
      }

      this.player.src([{
        src: videoUrl,
        type: videoType
      }]);
    },
    getUserInfo(){
      this.$http({
        method: 'get',
        url: 'user_info'
      }).then(res=>{
        if(res.code === 200){
          this.userInfo = res.data;
          // 判断会员状态：检查VIP等级和剩余天数
          this.is_see = this.isVipMember(this.userInfo, 'getUserInfo');
          console.log('用户信息:', this.userInfo);
          console.log('会员状态:', this.is_see);

          // 如果是VIP会员，重置时间限制状态
          if (this.is_see === 1) {
            console.log('检测到VIP会员，重置时间限制状态');
            this.is_time_limit_reached = false;
            this.total_watch_time = 0;

            // 重新启用播放器控件
            if (this.player) {
              this.player.controls(true);
            }
          }
          if(this.userInfo.status !== 1){
            this.$toast(this.$t("video.account_out"));
            localStorage.clear()
            this.$router.push({path:'/Auth'})
          }else {
            if(this.$store.getters.getBaseInfo.isplay == 1){
              this.getVideoInfo();
              this.getMoreVideoItem()
              // if(this.userInfo.money <= "0.00"){
              //   this.$toast(this.$t("video.buy"));
              //   this.$router.push({path:'/Home'})
              // }
            }else {
              this.getVideoInfo();
              this.getMoreVideoItem();
            }
          }
        }else if(res.code ===401){
          this.$toast(res.msg);
        }
      })
    },
    // 判断是否为VIP会员
    isVipMember(userInfo, source = '未知来源') {
      console.log(`VIP会员判断 [${source}] - 用户信息:`, userInfo);
      console.log('VIP等级:', userInfo.vip, '类型:', typeof userInfo.vip);
      console.log('VIP剩余天数原始值:', userInfo.vip_remaining_days, '类型:', typeof userInfo.vip_remaining_days);
      console.log('原始is_see字段:', userInfo.is_see);

      // 解析VIP等级 - 处理 "VIP1" 这种格式
      let vipLevel = 0;
      if (userInfo.vip !== undefined && userInfo.vip !== null) {
        if (typeof userInfo.vip === 'number') {
          vipLevel = userInfo.vip;
          console.log(`[${source}] VIP等级为数字:`, vipLevel);
        } else {
          // 提取VIP等级数字部分，支持 "VIP1", "1", "vip1" 等格式
          const vipMatch = String(userInfo.vip).match(/(\d+)/);
          if (vipMatch) {
            vipLevel = parseInt(vipMatch[1]) || 0;
            console.log(`[${source}] 从字符串 "${userInfo.vip}" 解析VIP等级:`, vipLevel);
          } else {
            console.log(`[${source}] 无法从 "${userInfo.vip}" 解析VIP等级`);
          }
        }
      } else {
        console.log(`[${source}] VIP字段为空或未定义:`, userInfo.vip);
      }

      // 解析剩余天数 - 处理 "30my.days" 这种格式
      let remainingDays = 0;
      if (userInfo.vip_remaining_days !== undefined && userInfo.vip_remaining_days !== null) {
        // 提取数字部分，支持 "30my.days" 或 "30" 格式
        const daysMatch = String(userInfo.vip_remaining_days).match(/(\d+)/);
        if (daysMatch) {
          remainingDays = parseInt(daysMatch[1]) || 0;
          console.log(`[${source}] 从 "${userInfo.vip_remaining_days}" 解析剩余天数:`, remainingDays);
        } else {
          console.log(`[${source}] 无法从 "${userInfo.vip_remaining_days}" 解析剩余天数`);
        }
      } else {
        console.log(`[${source}] VIP剩余天数字段为空或未定义:`, userInfo.vip_remaining_days);
      }

      console.log(`[${source}] 解析后VIP等级:`, vipLevel);
      console.log(`[${source}] 解析后剩余天数:`, remainingDays);

      // 只有VIP等级大于0且剩余天数大于0才是会员
      if (vipLevel > 0 && remainingDays > 0) {
        console.log(`[${source}] 判断结果: VIP会员 - 可以无限制观看`);
        return 1; // 会员
      }

      console.log(`[${source}] 判断结果: 非VIP会员（VIP0或无VIP） - 限制8秒观看`);
      return 0; // 非会员（包括VIP0）
    },

    // 强制刷新VIP状态
    forceRefreshVipStatus() {
      console.log('强制刷新VIP状态');
      this.getUserInfo();
    },
  },
  mounted(){
    const _this = this;
    if(!localStorage.getItem('token')){
      this.$router.push({path:'/Register'})
    }else {
      // 确保video元素存在后再初始化播放器
      this.$nextTick(() => {
        try {
          // 检查是否已经初始化过播放器
          if (this.player) {
            this.player.dispose();
            this.player = null;
          }

          this.player = videojs("my-video",  {
            height:"200px",
            preload: "auto", // 预加载
            controls: true,  // 显示播放的控件
            multipleArray: [0.75, 1, 1.5, 2], // 倍速设置
            playsinline: true, // 移动端内联播放
            fluid: true, // 响应式
            responsive: true
          },function(){
            this.on("play",() => {
              console.log('播放事件触发 - is_see:', _this.is_see, 'is_time_limit_reached:', _this.is_time_limit_reached);
              console.log('当前用户信息:', _this.userInfo);

              // 播放前重新检查VIP状态（仅在用户信息完整时）
              if (_this.userInfo && _this.userInfo.vip !== undefined) {
                const newVipStatus = _this.isVipMember(_this.userInfo, '播放事件');
                console.log('播放事件VIP检查结果:', newVipStatus, '当前状态:', _this.is_see);

                // 只有当新状态更有利时才更新（防止VIP状态被错误降级）
                if (newVipStatus === 1 || _this.is_see === 0) {
                  _this.is_see = newVipStatus;
                  console.log('更新VIP状态为:', _this.is_see);
                } else {
                  console.log('保持当前VIP状态:', _this.is_see);
                }
              } else {
                console.log('用户信息不完整，跳过VIP状态检查');
              }

              // 如果是VIP会员，确保重置限制状态
              if(_this.is_see === 1) {
                console.log('确认VIP会员身份，重置所有限制');
                _this.is_time_limit_reached = false;
                _this.total_watch_time = 0;
                this.controls(true);
              }

              // 如果是非VIP用户且已达到时间限制，立即暂停
              if(_this.is_see == 0 && _this.is_time_limit_reached) {
                console.log('非VIP用户已达到8秒限制，禁止继续播放');
                this.pause();
                _this.$toast(_this.$t("video.member_limit"));
                return;
              }

              _this.is_play=true;
            });

            this.on("pause",() => {
              _this.is_play=false;
            });

            // 添加错误处理
            this.on("error", (error) => {
              console.error('Video.js播放错误:', error);
              const mediaError = _this.player.error();

              if (mediaError) {
                console.error('媒体错误代码:', mediaError.code);
                console.error('媒体错误信息:', mediaError.message);

                // 根据错误类型提供不同的处理方案
                switch (mediaError.code) {
                  case 1: // MEDIA_ERR_ABORTED
                    console.log('视频播放被中止');
                    break;
                  case 2: // MEDIA_ERR_NETWORK
                    console.log('网络错误，无法加载视频');
                    _this.$toast('网络错误，请检查网络连接');
                    break;
                  case 3: // MEDIA_ERR_DECODE
                    console.log('视频解码错误');
                    _this.$toast('视频格式不支持，请联系客服');
                    break;
                  case 4: // MEDIA_ERR_SRC_NOT_SUPPORTED
                    console.log('视频源不支持');
                    _this.$toast('视频格式不支持或文件损坏');
                    break;
                  default:
                    console.log('未知视频错误');
                    _this.$toast('视频播放失败，请稍后重试');
                    break;
                }
              }
            });
          });
        } catch (error) {
          console.error('视频播放器初始化失败:', error);
        }
      });

      this.getUserInfo();

      // 添加额外的VIP状态检查间隔
      this.vipCheckTimer = setInterval(() => {
        if (this.userInfo && this.userInfo.vip) {
          const newVipStatus = this.isVipMember(this.userInfo, '定时检查');
          if (newVipStatus !== this.is_see) {
            console.log('检测到VIP状态变化，从', this.is_see, '变为', newVipStatus);
            this.is_see = newVipStatus;

            if (this.is_see === 1) {
              console.log('升级为VIP会员，移除所有观看限制');
              this.is_time_limit_reached = false;
              this.total_watch_time = 0;
              if (this.player) {
                this.player.controls(true);
              }
            }
          }
        }
      }, 2000); // 每2秒检查一次VIP状态

      this.times = setInterval(() => {
        if(this.is_play && this.is_see == 0 && !this.is_time_limit_reached){
          const ct = Math.round(this.player.currentTime())

          // 累计总观看时间
          if(this.is_play) {
            this.total_watch_time += 1;
          }

          console.log('非VIP用户观看时间检查 - 当前播放时间:', ct, '秒, 总观看时间:', this.total_watch_time, '秒, is_see:', this.is_see);

          // 检查总观看时间是否超过8秒
          if(this.total_watch_time >= 8){
            console.log('非VIP用户总观看时间超过8秒，永久禁止播放');
            this.is_time_limit_reached = true;
            this.player.pause();

            // 禁用播放器控件
            this.player.controls(false);

            this.$toast(this.$t("video.member_limit"));
            return;
          }
        }
      }, 1000);
    }

  },

  destroyed () {
    if(this.is_play){
      this.is_play = false;
    }

    // 清理所有定时器
    clearInterval(this.times);
    clearInterval(this.vipCheckTimer);

    // 正确销毁video.js播放器
    if (this.player) {
      try {
        this.player.dispose();
        this.player = null;
      } catch (error) {
        console.error('销毁视频播放器失败:', error);
      }
    }
  }
};
</script>

<style scoped>
.video-js {
  width: 100%;
  /*height: 420px;*/
  font-size: 24px;
}
.movie-content{
  flex: 1;
  overflow-y: auto;
}
.movie-content .movie-descript{
  width: 100%;
  height: 140px;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
  padding: 15px;
}
.movie-content .movie-descript p{
  font-size: 30px;
  font-weight: 700;
  color: #000;
}
.movie-content .movie-descript span{
  color: #979799;
}
.movie-content .movie-body{
  width: calc(100% - 20px);
  margin: 0 auto;
}
::v-deep .movie-video .video-js .vjs-big-play-button {
  top: 50%;
  left: 50%;
  margin-top: -50px;
  margin-left: -100px;
}
.movie-content .movie-body .movie-title{
  height: 70px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.movie-content .movie-body .movie-title>div:first-child {
  width: 410px;
}
.movie-content .movie-body .movie-title>div:first-child span{
  display: flex;
  align-items: center;
  justify-content: flex-start;
  font-size: 30px;
  font-weight: 700;
  color: #000;
}
.movie-content .movie-body .movie-title>div:first-child span:before {
  content: "";
  display: block;
  width: 8px;
  height: 30px;
  background-color: #f487e0;
  border-radius: 25px;
  margin-right: 10px;
}
.movie-play-item{
  width: 100%;
  height: 200px;
  border-radius: 10px;
  position: relative;
  display: flex;
  background-color: #fff;
  margin-bottom: 20px;
}
.movie-play-item>div{
  height: 100%;
}
.movie-play-item>div:first-child {
  width: 200px;
  position: relative;
}
.movie-play-item>div:first-child>img{
  width: 100%;
  height: 100%;
  border-radius: 10px 0 0 10px;
}
.movie-play-item>div:first-child>div{
  position: absolute;
  width: 100%;
  height: 30px;
  bottom: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 10px;
  background-color: rgba(0,0,0,.4);
  border-radius: 0 0 0 10px;
}
.movie-play-item>div:first-child>div .van-count-down {
  color: #fff;
  font-size: 25px;
}
.movie-play-item>div:nth-child(2) p{
  width: 500px;
  height: 60px;
  font-size: 30px;
  line-height: 32px;
  word-break: break-all;
  overflow: hidden;
  color: #000;
}
.movie-play-item>div:nth-child(2) span{
  color: #000;
}
.movie-play-item>div:nth-child(2) {
  padding: 20px;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: space-between;
}
abbr, address, article, aside, audio, b, blockquote, body, canvas, caption, cite, code, dd, del, details, dfn, div, dl, dt, em, fieldset, figcaption, figure, footer, form, h1, h2, h3, h4, h5, h6, header, hgroup, html, i, iframe, img, ins, kbd, label, legend, li, mark, menu, nav, object, ol, p, pre, q, samp, section, small, span, strong, sub, summary, sup, table, tbody, td, tfoot, th, thead, time, tr, ul, var, video {
  margin: 0;
  padding: 0;
  border: 0;
  outline: 0;
  font-size: 100%;
  vertical-align: baseline;
  box-sizing: border-box;
}

.vjs-big-play-button .vjs-icon-placeholder {
  font-size: 1.63em !important;
}
</style>