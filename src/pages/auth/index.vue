<template>
  <div class="auth-container page">
    <!-- 滚动背景图 -->
    <div class="bg-scroll-container">
      <div class="bg-scroll-img" :style="backgroundStyle"></div>
      <div class="bg-scroll-img" :style="backgroundStyle"></div>
    </div>
    
    <!-- 内容区域 -->
    <div class="content-wrapper">
      <!-- 顶部选项卡 -->
      <div class="tab-container">
        <div class="tab-item" :class="{ active: activeTab === 'login' }" @click="setActiveTab('login')">
          <span>{{ $t('auth.login') }}</span>
        </div>
        <div class="tab-item" :class="{ active: activeTab === 'register' }" @click="setActiveTab('register')">
          <span>{{ $t('auth.register') }}</span>
        </div>
      </div>

      <!-- 登录表单 -->
      <div class="form-container" v-if="activeTab === 'login'">
        <div class="form-wrapper">
          <van-field
            v-model="loginForm.username"
            clearable
            input-align="center"
            class="input"
            :placeholder="$t('auth.username_place')"
            type="tel"
            maxlength="10"
            @input="onUsernameInput"
          />
          <van-field
            v-model="loginForm.password"
            :type="passwordType"
            input-align="center"
            class="input"
            :placeholder="$t('auth.pwd_place')"
          >
            <template slot="right-icon">
              <van-icon
                :name="passwordType === 'password' ? 'closed-eye' : 'eye-o'"
                @click="switchPasswordType"
              />
            </template>
          </van-field>
          
          <div class="reset-text">
            <span>{{ $t('auth.forgetpwd') }}?</span>
          </div>
          
          <van-button
            class="auth-btn"
            type="primary"
            size="normal"
            @click="doLogin()"
          >
            {{ $t('auth.login') }}
          </van-button>
        </div>
      </div>

      <!-- 注册表单 -->
      <div class="form-container" v-if="activeTab === 'register'">
        <div class="form-wrapper">
          <van-field
            v-model="registerForm.username"
            clearable
            input-align="center"
            class="input"
            :placeholder="$t('auth.username_place')"
            type="tel"
            maxlength="10"
            @input="onUsernameInput"
          />
          <van-field
            v-model="registerForm.password"
            :type="passwordType"
            input-align="center"
            class="input"
            :placeholder="$t('auth.pwd_place')"
          >
            <template slot="right-icon">
              <van-icon
                :name="passwordType === 'password' ? 'closed-eye' : 'eye-o'"
                @click="switchPasswordType"
              />
            </template>
          </van-field>
          <van-field
            v-model="registerForm.code"
            clearable
            input-align="center"
            class="input"
            :placeholder="$t('auth.invite_code_place')"
          />
          
          <div class="agreement">
            <div class="custom-checkbox" @click="toggleAgreement">
              <div class="checkbox-circle" :class="{ 'checked': checked }">
                <van-icon v-if="checked" name="success" class="check-icon" />
              </div>
              <span class="agreement-text">{{ $t('auth.agreement_place') }}</span>
            </div>
          </div>
          
          <van-button
            class="auth-btn"
            type="primary"
            size="normal"
            @click="doRegister()"
          >
            {{ $t('auth.register') }}
          </van-button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'AuthPage',
  data() {
    return {
      activeTab: 'login', // 默认显示登录
      passwordType: 'password',
      checked: true,
      lang: 'zh_tw',
      loginForm: {
        username: '',
        password: ''
      },
      registerForm: {
        username: '',
        password: '',
        code: ''
      }
    };
  },
  computed: {
    backgroundStyle() {
      // 尝试多种方式加载 hklogin.jpg
      let imagePath;
      try {
        // 方法1: 使用 require 导入
        imagePath = require('@/../public/img/login/hklogin.jpg');
      } catch (e) {
        // 方法2: 使用相对路径
        imagePath = '/img/login/hklogin.jpg';
      }

      return {
        backgroundImage: `url(${imagePath})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center',
        backgroundRepeat: 'no-repeat'
      };
    }
  },
  mounted() {
    this.lang = localStorage.getItem('lang') || 'zh_tw';
  },
  methods: {
    setActiveTab(tab) {
      this.activeTab = tab;
    },
    switchPasswordType() {
      this.passwordType = this.passwordType === 'password' ? 'text' : 'password';
    },
    onUsernameInput(value) {
      // 只允许输入数字，移除所有非数字字符
      if (this.activeTab === 'login') {
        this.loginForm.username = value.replace(/[^0-9]/g, '');
      } else {
        this.registerForm.username = value.replace(/[^0-9]/g, '');
      }
    },
    toggleAgreement() {
      this.checked = !this.checked;
    },
    doLogin() {
      if (!this.loginForm.username) {
        this.$toast.fail(this.$t('auth.username_place'));
        return false;
      }
      if (!/^[0-9]{10}$/.test(this.loginForm.username)) {
        this.$toast.fail(this.$t('auth.username_format_error'));
        return false;
      }
      if (!this.loginForm.password) {
        this.$toast.fail(this.$t('auth.pwd_place'));
        return false;
      }
      
      this.$http({
        url: 'member_login',
        method: 'post',
        data: {
          username: this.loginForm.username,
          password: this.loginForm.password,
          lang: this.lang
        }
      }).then((res) => {
        if (res.code === 200) {
          this.$toast.success(res.msg);
          localStorage.setItem('token', res.data.id);
          this.$router.push('Mine');
        } else {
          this.$toast.fail(res.msg);
        }
      }).catch(err => {
        console.error('登录请求失败:', err);
        this.$toast.fail('網絡請求失敗，請稍後重試');
      });
    },
    doRegister() {
      if (!this.registerForm.username) {
        this.$toast.fail(this.$t('auth.username_place'));
        return false;
      }
      if (!/^[0-9]{10}$/.test(this.registerForm.username)) {
        this.$toast.fail(this.$t('auth.username_format_error'));
        return false;
      }
      if (!this.registerForm.password) {
        this.$toast.fail(this.$t('auth.pwd_place'));
        return false;
      }
      if (!this.registerForm.code) {
        this.$toast.fail(this.$t('auth.invite_code_place'));
        return false;
      }
      if (!this.checked) {
        this.$toast.fail(this.$t('auth.agreement'));
        return false;
      }
      
      this.$http({
        method: 'post',
        data: {
          username: this.registerForm.username,
          password: this.registerForm.password,
          code: this.registerForm.code,
          lang: this.lang
        },
        url: 'member_register'
      }).then(res => {
        if (res.code === 200) {
          this.$toast.success(res.msg);
          localStorage.setItem('token', res.data);
          this.$router.push('Mine');
        } else {
          this.$toast.fail(res.msg);
        }
      }).catch(err => {
        console.error('注册请求失败:', err);
        this.$toast.fail('網絡請求失敗，請稍後重試');
      });
    }
  },
  created() {
    if (localStorage.getItem('token')) {
      this.$router.push('Mine');
    }
  }
};
</script>

<style lang="less" scoped>
@import "../../assets/css/base.css";

.auth-container {
  position: relative;
  height: 100vh;
  overflow: hidden;
}

/* 滚动背景 */
.bg-scroll-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 200%; /* 两倍高度用于滚动 */
  z-index: 1;
  animation: scrollBackground 20s linear infinite;
}

.bg-scroll-img {
  width: 100%;
  height: 50%; /* 每张图片占容器的一半 */
  display: block;
}

@keyframes scrollBackground {
  0% {
    transform: translateY(0);
  }
  100% {
    transform: translateY(-50%); /* 向上滚动一半的距离 */
  }
}

/* 内容区域 */
.content-wrapper {
  position: relative;
  z-index: 2;
  height: 100%;
  display: flex;
  flex-direction: column;
  background: rgba(0, 0, 0, 0.3); /* 半透明遮罩 */
}

/* 顶部选项卡 */
.tab-container {
  display: flex;
  margin-top: 100px;
  margin-bottom: 50px;
  padding: 0 60px;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 20px 0;
  border-bottom: 3px solid transparent;
  cursor: pointer;
  transition: all 0.3s ease;
}

.tab-item span {
  font-size: 36px;
  font-weight: 600;
  color: rgba(255, 255, 255, 0.6);
  transition: color 0.3s ease;
}

.tab-item.active {
  border-bottom-color: #7e5678;
}

.tab-item.active span {
  color: #fff;
}

/* 表单容器 */
.form-container {
  flex: 1;
  padding: 0 60px;
  display: flex;
  align-items: center;
}

.form-wrapper {
  width: 100%;
}

/* 输入框样式 */
.input {
  padding: 10px 20px;
  margin-bottom: 30px;
  border-radius: 50px;
  text-align: center;
  line-height: 80px;
  font-size: 30px;
  color: #4e4e4e;
  background: rgba(255, 255, 255, 0.9);
}

::v-deep .van-field__right-icon .van-icon {
  font-size: 50px;
}

::v-deep .van-icon {
  font-size: 50px;
}

/* 重置密码文本 */
.reset-text {
  margin: 20px 15px 40px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

.reset-text span {
  color: #fff;
  font-size: 25px;
  font-weight: 500;
}

/* 协议复选框 */
.agreement {
  display: flex;
  align-items: center;
  margin-bottom: 40px;
  padding: 0 15px;
}

.custom-checkbox {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

.checkbox-circle {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  border: 3px solid rgba(255, 255, 255, 0.6);
  background-color: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-right: 15px;
  position: relative;
  overflow: hidden;
}

.checkbox-circle:hover {
  border-color: rgba(255, 255, 255, 0.8);
  transform: scale(1.05);
}

.checkbox-circle.checked {
  background-color: #7e5678;
  border-color: #7e5678;
  box-shadow: 0 0 20px rgba(126, 86, 120, 0.5);
  transform: scale(1.1);
}

.checkbox-circle.checked::before {
  content: '';
  position: absolute;
  top: -5px;
  left: -5px;
  right: -5px;
  bottom: -5px;
  border-radius: 50%;
  background: rgba(126, 86, 120, 0.2);
  animation: pulse 2s infinite;
}

.checkbox-circle.checked:hover {
  transform: scale(1.15);
}

.check-icon {
  color: #fff;
  font-size: 20px;
  font-weight: bold;
  animation: checkmark 0.3s ease-in-out;
}

@keyframes checkmark {
  0% {
    opacity: 0;
    transform: scale(0.3) rotate(-45deg);
  }
  50% {
    opacity: 1;
    transform: scale(1.2) rotate(0deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 0.7;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.3;
  }
  100% {
    transform: scale(1);
    opacity: 0.7;
  }
}

.agreement-text {
  color: #fff;
  font-size: 24px;
  line-height: 1.4;
  flex: 1;
}

/* 按钮样式 */
.auth-btn {
  width: 100%;
  height: 100px;
  border-radius: 50px;
  color: #fff;
  background-color: #7e5678;
  font-size: 30px;
  font-weight: bolder;
  border: none;
  margin-top: 20px;
}

.auth-btn:active {
  background-color: #6a4a64;
}
</style>
