<template>
  <div class="container page">
    <van-nav-bar title="打賞主播" class="nav-bar">
      <template #left>
        <van-icon name="arrow-left" color="#fff" @click="back()"/>
      </template>
      <template #right>
        <div class="right">預約打賞</div>
      </template>
    </van-nav-bar>
    <div class="record">
        <div class="period">
            <!-- 左侧主播信息 -->
            <div class="anchor-info">
              <van-image class="cover" src="img/lottery/shaizi/zhubo1.jpg">
                <template v-slot:loading>
                  <van-loading type="spinner"/>
                </template>
              </van-image>
              <div class="anchor-details">
                <div class="anchor-name">距離編碼</div>
                <div class="anchor-id">{{parseInt(this.lottery.now_expect || 0) + 1}}</div>
              </div>
            </div>

            <!-- 右侧PK截止信息 -->
            <div class="pk-deadline">
              <div class="pk-title">直播pk截止</div>
              <div class="countdown">
                <van-count-down :time="time" @finish="check()" />
              </div>
            </div>

        </div>
        <div class="linear-gradient" style="background: linear-gradient(to right, rgba(126, 86, 120, 0), rgb(230, 195, 161), rgba(126, 86, 120, 0));"></div>
        <div class="recent">
          <div class="pk-result-display" @click="toggleHistory">
            <!-- PK结果显示 -->
            <div class="pk-info">
              <div class="pk-result-text">
                第 <span class="highlight">{{lottery.now_expect || '---'}}</span> 編碼
              </div>
              <div class="pk-winner-text">
                PK獲勝主播：<span class="winner-name">{{getWinnerText(lottery.opencode)}}</span>
              </div>
            </div>
            <van-icon name="arrow-down" :class="{ up: active, down: !active }" />
          </div>

          <!-- 历史开奖记录区域 -->
          <div class="history-section" :class="{ active: active }">
            <div class="history-header">
              <div class="header-item">編碼</div>
              <div class="header-item">正確打賞</div>
            </div>
            <div class="history-list">
              <div v-for="(record, index) in lottery_list" :key="index" class="history-item">
                <div class="history-code">{{ record.expect || '---' }}</div>
                <div class="history-result">{{ getWinnerText(record.opencode) }}</div>
              </div>
              <div v-if="!lottery_list || lottery_list.length === 0" class="history-item">
                <div class="history-code">暂无数据</div>
                <div class="history-result">---</div>
              </div>
            </div>
          </div>
        </div>
    </div>
    <div class="history_popup"></div>
    <div class="wrapper">
        <div class="options-bar">
            <div class="game">
              <div class="tips" style="display: none;">
                <p class="odds">【{{$mapKoreanReactive(this.lottery.desc)}}】</p>
                <div class="play-tip" >
                  <van-icon name="more-o" />
<!--                  <span class="span-text" @click="playgame = true">玩法提示</span>-->
                  <span class="span-text" @click="$router.push({path:'/GameRecord'});">{{$t("my.task_record")}}</span>
                  <van-popup  class="mask" get-container="body" v-model="playgame">
                      <div class="play-type-tip">
                        <div class="title">玩法规则</div>
                        <div class="wrapper">
                          <div class="item">
                              <van-icon name="info-o" />
                              <div class="content">
                                <p class="content-title">玩法提示</p>
                                <p class="content-detail">从可选和值形态里面选择号码进行下注</p>
                              </div>
                          </div>
                          <div class="item">
                            <van-icon name="comment-o" />
                            <div class="content">
                              <p class="content-title">中奖说明</p>
                              <p class="content-detail">三个开奖号码总和值11~18 为大;总和值3~ 10为小;</p>
                            </div>
                          </div>
                          <div class="item">
                            <van-icon name="description" />
                            <div class="content">
                              <p class="content-title">投注范例</p>
                              <p class="content-detail">投注方案：小 开奖号码：123,即中小</p>
                            </div>
                          </div>
                        </div>
                      </div>
                  </van-popup>
                </div>
              </div>
              <div class="linear-gradient" style="background: linear-gradient(to right, rgba(126, 86, 120, 0), rgb(230, 195, 161), rgba(126, 86, 120, 0));"></div>
              <!-- 蝴蝶投注选项 -->
              <div class="butterfly-options">
                <!-- 第一行：蝴、蝶 -->
                <div class="butterfly-row">
                  <div class="butterfly-item" :class="{active:choose['big']}" @click="choosePlay('big','蝴');">
                    <img src="img/lottery/shaizi/p1.png" alt="蝴" class="butterfly-image" />
                    <div class="butterfly-text">蝴</div>
                  </div>
                  <div class="butterfly-item" :class="{active:choose['small']}" @click="choosePlay('small','蝶');">
                    <img src="img/lottery/shaizi/p2.png" alt="蝶" class="butterfly-image" />
                    <div class="butterfly-text">蝶</div>
                  </div>
                </div>

                <!-- 第二行：蝶.连理枝、蝶.寄相思 -->
                <div class="butterfly-row">
                  <div class="butterfly-item" :class="{active:choose['odd']}" @click="choosePlay('odd','蝶.连理枝');">
                    <img src="img/lottery/shaizi/p3.png" alt="蝶.连理枝" class="butterfly-image" />
                    <div class="butterfly-text">蝶.连理枝</div>
                  </div>
                  <div class="butterfly-item" :class="{active:choose['even']}" @click="choosePlay('even','蝶.寄相思');">
                    <img src="img/lottery/shaizi/p4.png" alt="蝶.寄相思" class="butterfly-image" />
                    <div class="butterfly-text">蝶.寄相思</div>
                  </div>
                </div>

                <!-- 第三行：蝶.比翼鸟、蝶.化蝶飞 -->
                <div class="butterfly-row">
                  <div class="butterfly-item" :class="{active:choose['butterfly_bird']}" @click="choosePlay('butterfly_bird','蝶.比翼鸟');">
                    <img src="img/lottery/shaizi/p5.png" alt="蝶.比翼鸟" class="butterfly-image" />
                    <div class="butterfly-text">蝶.比翼鸟</div>
                  </div>
                  <div class="butterfly-item" :class="{active:choose['butterfly_fly']}" @click="choosePlay('butterfly_fly','蝶.化蝶飞');">
                    <img src="img/lottery/shaizi/p6.png" alt="蝶.化蝶飞" class="butterfly-image" />
                    <div class="butterfly-text">蝶.化蝶飞</div>
                  </div>
                </div>
              </div>

              <!-- 主播图片 -->
              <div class="zhubo-image-container">
                <img src="/img/lottery/shaizi/zhubo4.png" alt="主播" class="zhubo-image" />
              </div>


            </div>
        </div>
        <div class="bottom-bar">
          <div class="bar">
            <div class="left">
              <div class="item" @click="shopping ? shopping = false : shopping = true ">
                <van-icon name="cart-o" class="jixuanico" />
                <span class="text">打賞清單</span>
              </div>
              <div class="line"></div>
            </div>
            <div class="mid">
              <span class="text">可用積分</span>
              <span class="text num">{{ formatMoney(this.userInfo.money) }}</span>
              <span class="text">積分</span>
            </div>
            <div class="right" @click="jiesuan()">
              打賞
            </div>
          </div>
          <div class="wrapper" :class="{active:shopping}">
               <div class="item">
                 <span class="label">當前選擇：</span>
                 <div class="bet-number">{{ this.shopchoose}}</div>
                 <van-icon name="arrow-down" :class="{ up: !shopping,down:shopping }" @click="shopping ? shopping = false : shopping = true" />
               </div>
              <div class="item">
                <span class="label">打賞積分</span>
                <div class="amount-wrapper">
                  <van-field v-model="money" type="digit" placeholder="請輸入積分" />
                  <span class="label">積分</span>
                </div>
              </div>
              <div class="item">
                <div class="part">
                  <span>一共打賞</span>
                  <span class="number">{{this.formData.length}}</span>
                  <span>個主播</span>
                </div>
                <div class="part">
                  <span>合計</span>
                  <span class="number">{{this.formData.length * this.money}}</span>
                  <span>積分</span>
                </div>

              </div>
          </div>
        </div>
      <van-popup v-model="jiesuanpage" get-container="body" >
        <div class="confirm-order-modal">
            <div class="head van-hairline--bottom">
              <p class="text">打賞清單</p>
            </div>
            <ui class="list">
                <li v-for="(v,key) in formData" :key="key" class="lise-item van-hairline--bottom">
                    <div class="main">
                      <p class="bet-name">{{ v.name }}</p>
                      <p class="detail-text">1個主播X{{ money }}積分={{ money }}積分</p>
                    </div>
                    <van-icon @click="clearChooes(v.type)" name="close" />
                </li>
            </ui>
            <div class="sub-bar">
              <van-button class="item cancel-btn" type="default" @click="allClear()" :disabled="isSubmitting">清空清單</van-button>
              <van-button class="item sub-btn" type="default" @click="doSub" :loading="isSubmitting" :disabled="isSubmitting">
                {{ isSubmitting ? "打賞中" : "打賞" }}
              </van-button>
            </div>
        </div>
      </van-popup>
      <!-- 详细历史记录弹出窗口已移除 -->
    </div>
  </div>
</template>

<script>
var time;
var count = 0;
export default {
  data() {
    return {
      jiesuanpage:false,
      choose: {
        big: false,
        small: false,
        odd: false,
        even: false,
        butterfly_bird: false,
        butterfly_fly: false
      },
      playgame:false,
      shopping:false,
      isLoading: false,
      play:{},
      lottery_peilv_list:{},
      lottery_list:[],
      active: false,
      userInfo:{},
      lottery:{},
      shanzi_1:"0",
      shanzi_2:"0",
      shanzi_3:"0",
      dice1: 1,
      dice2: 1,
      dice3: 1,
      sum:0,
      size:"",
      double:"",
      time:0,
      shopchoose:"未選擇",
      gameitem:"",
      formData:[],
      money:"",
      isRolling: false, // 骰子滚动状态
      isSubmitting: false, // 防止重复提交
      submittedOrders: new Set(), // 记录已提交的订单（期号+投注项+金额）
    };
  },
  computed: {
    // 蝴蝶投注选项
    butterflyOptions() {
      if (!this.lottery_peilv_list) return [];
      const butterflyTypes = ['big', 'small', 'odd', 'even', 'butterfly_bird', 'butterfly_fly'];
      if (Array.isArray(this.lottery_peilv_list)) {
        return this.lottery_peilv_list.filter(item => butterflyTypes.includes(item.type));
      } else {
        return Object.values(this.lottery_peilv_list).filter(item => butterflyTypes.includes(item.type));
      }
    }
  },
  methods: {
    // 格式化金额为香港格式
    formatMoney(amount) {
      if (!amount && amount !== 0) return '0';
      const num = parseFloat(amount);
      if (isNaN(num)) return '0';

      // 如果是整数，不显示小数点
      if (num % 1 === 0) {
        return num.toLocaleString('en-HK');
      } else {
        return num.toLocaleString('en-HK', {
          minimumFractionDigits: 2,
          maximumFractionDigits: 2
        });
      }
    },
    back(){
      return window.history.back();
    },
    toggleHistory() {
      // 只切换简单历史记录的显示，不显示详细的弹出窗口
      this.active = !this.active;
    },
    getWinnerText(opencode) {
      // 简化处理，直接返回固定格式的获胜主播文本
      if (!opencode) return '蝶，蝶.連理枝';

      try {
        // 确保opencode是字符串
        const codeStr = String(opencode);
        const codes = codeStr.split(',');

        if (codes.length >= 2) {
          const dice1 = parseInt(codes[0]) || 1;
          const dice2 = parseInt(codes[1]) || 1;
          const sum = dice1 + dice2;

          // 判断大小
          const size = sum >= 8 ? '蝶' : '蝴';

          // 根据和值选择主播名称
          let hostName = '';
          if (sum <= 6) {
            hostName = '連理枝';
          } else if (sum <= 9) {
            hostName = '寄相思';
          } else {
            hostName = '連理枝';
          }

          return `${size}，蝶.${hostName}`;
        }
      } catch (error) {
        // 静默处理错误
      }

      return '蝶，蝶.連理枝';
    },
    doSub(){
      // 防止重复提交
      if(this.isSubmitting) {
        this.$toast(this.$t("reservation.processing_wait"));
        return false;
      }

      if(this.money === "0"){
        this.$toast(this.$t("reservation.money_err"));
        return false;
      }
      if(this.formData.length === 0){
        this.$toast(this.$t("reservation.choose_num"));
        return false;
      }else if(this.money === ""){
        this.$toast(this.$t("reservation.price_place"));
        return false;
      } else {
        if(this.userInfo.money - (this.money * this.formData.length) < 0 ){
          this.$toast(this.$t("reservation.balance_enough"));
          return false;
        }else {
          // 检查是否已经提交过相同的订单
          // 为每个投注项生成独立的订单键
          const currentItems = this.gameitem.split(',');
          let hasDuplicateOrder = false;

          console.log('检查重复下注 - 当前期号:', this.lottery.now_expect);
          console.log('检查重复下注 - 投注项:', currentItems);
          console.log('检查重复下注 - 金额:', this.money);
          console.log('检查重复下注 - 已提交订单:', Array.from(this.submittedOrders));

          for(let item of currentItems) {
            const orderKey = `${this.lottery.now_expect}_${item.trim()}_${this.money}`;
            console.log('检查订单键:', orderKey);
            if(this.submittedOrders.has(orderKey)) {
              const itemName = this.getItemDisplayName(item.trim());
              const errorMsg = this.$t("reservation.duplicate_bet_error")
                .replace('{item}', itemName)
                .replace('{amount}', this.money);
              this.$toast(errorMsg);
              hasDuplicateOrder = true;
              break;
            }
          }

          if(hasDuplicateOrder) {
            return false;
          }

          // 设置提交状态
          this.isSubmitting = true;

          this.$http({
            method: 'post',
            data:{
               item:this.gameitem,
               money:this.money,
               lid:this.lottery.id,
               mid:this.userInfo.id,
               expect:this.lottery.now_expect
            },
            url: 'game_place_order'
          }).then(res=>{
            if(res.code === 200){
              this.$toast(res.msg);
              // 记录已提交的订单
              const submittedItems = this.gameitem.split(',');
              console.log('记录已提交订单 - 投注项:', submittedItems);
              console.log('记录已提交订单 - 金额:', this.money);
              for(let item of submittedItems) {
                const orderKey = `${this.lottery.now_expect}_${item.trim()}_${this.money}`;
                this.submittedOrders.add(orderKey);
                console.log('已记录订单键:', orderKey);
              }
              console.log('当前已提交订单列表:', Array.from(this.submittedOrders));
              this.allClear();
              this.getUserInfo();
            }else if(res.code === 401){
              this.$toast(res.msg);
            }
          }).catch(err => {
            console.error('下注失败:', err);
            this.$toast(this.$t("reservation.bet_failed_retry"));
          }).finally(() => {
            // 重置提交状态
            this.isSubmitting = false;
          });
          return true;
        }
      }
    },
    allClear(){
      for(var i = 0;i<this.formData.length;i++){
          this.choose[this.formData[i]['type']] = false;
      }
      this.formData.length = 0;
      this.money = "";
      this.shopchoose = "未選擇";
      this.gameitem ="";
      this.shopping = false;
      this.jiesuanpage = false;
      // 重置提交状态
      this.isSubmitting = false;
    },
    jiesuan(){
      if(this.formData.length === 0){
        this.$toast(this.$t("reservation.choose_num"));
        return false;
      }else if(this.money === ""){
        this.$toast(this.$t("reservation.price_place"));
        return false;
      }
      else {
        this.jiesuanpage ? this.jiesuanpage = false : this.jiesuanpage = true;
      }

    },
    clearChooes(type){
      for(var i = 0;i<this.formData.length;i++){
        if(type === this.formData[i]['type'] ){
          this.formData.splice(i,1)
          this.choose[type] = false;
        }
      }
      if(this.formData.length >= 1){
        for(var j = 0;j < this.formData.length;j++){
          if(j === 0){
            this.shopchoose = this.formData[j]['name'];
            this.gameitem = this.formData[j]['type'];
          }else {
            this.shopchoose += ","+this.formData[j]['name'];
            this.gameitem   += "," + this.formData[j]['type'];
          }
        }
      }else {
        this.shopchoose = "未選擇";
        this.gameitem = "";
        this.shopping = false;
      }
      if(this.formData.length === 0){
        this.jiesuanpage = false;
      }
    },
    // 安全获取 opencode 数据
    getOpencode(item, index) {
      if (!item || !item.opencode || !Array.isArray(item.opencode)) {
        return 1; // 默认返回1
      }
      return parseInt(item.opencode[index]) || 1;
    },

    // 计算骰子总和
    getDiceSum(item) {
      if (!item || !item.opencode || !Array.isArray(item.opencode)) {
        return 0;
      }
      const dice1 = parseInt(item.opencode[0]) || 0;
      const dice2 = parseInt(item.opencode[1]) || 0;
      const dice3 = parseInt(item.opencode[2]) || 0;
      return dice1 + dice2 + dice3;
    },

    // 获取骰子大小文本（蝴蝶主题）
    getDiceSizeText(item) {
      const sum = this.getDiceSum(item);
      if (sum >= 11 && sum <= 18) {
        return '蝴';
      } else {
        return '蝶';
      }
    },

    // 获取骰子奇偶文本（蝴蝶主题）
    getDiceOddEvenText(item) {
      const sum = this.getDiceSum(item);
      if (sum % 2 === 0) {
        return '蝶.寄相思';
      } else {
        return '蝶.连理枝';
      }
    },

    // 获取骰子emoji
    getDiceEmoji(value) {
      const diceEmojis = {
        1: '⚀',
        2: '⚁',
        3: '⚂',
        4: '⚃',
        5: '⚄',
        6: '⚅'
      };
      return diceEmojis[value] || '⚀';
    },
    // 获取骰子图像URL
    getDiceImageUrl(value) {
      // 使用立体骰子图像
      const diceImages = {
        1: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggOEg1NlY1Nkg4VjhaIiBmaWxsPSIjRkZGRkZGIiBzdHJva2U9IiMwMDAiIHN0cm9rZS13aWR0aD0iMiIvPgo8Y2lyY2xlIGN4PSIzMiIgY3k9IjMyIiByPSI0IiBmaWxsPSIjMDAwIi8+Cjwvc3ZnPgo=',
        2: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggOEg1NlY1Nkg4VjhaIiBmaWxsPSIjRkZGRkZGIiBzdHJva2U9IiMwMDAiIHN0cm9rZS13aWR0aD0iMiIvPgo8Y2lyY2xlIGN4PSIyMCIgY3k9IjIwIiByPSI0IiBmaWxsPSIjMDAwIi8+CjxjaXJjbGUgY3g9IjQ0IiBjeT0iNDQiIHI9IjQiIGZpbGw9IiMwMDAiLz4KPC9zdmc+Cg==',
        3: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggOEg1NlY1Nkg4VjhaIiBmaWxsPSIjRkZGRkZGIiBzdHJva2U9IiMwMDAiIHN0cm9rZS13aWR0aD0iMiIvPgo8Y2lyY2xlIGN4PSIyMCIgY3k9IjIwIiByPSI0IiBmaWxsPSIjMDAwIi8+CjxjaXJjbGUgY3g9IjMyIiBjeT0iMzIiIHI9IjQiIGZpbGw9IiMwMDAiLz4KPGNpcmNsZSBjeD0iNDQiIGN5PSI0NCIgcj0iNCIgZmlsbD0iIzAwMCIvPgo8L3N2Zz4K',
        4: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggOEg1NlY1Nkg4VjhaIiBmaWxsPSIjRkZGRkZGIiBzdHJva2U9IiMwMDAiIHN0cm9rZS13aWR0aD0iMiIvPgo8Y2lyY2xlIGN4PSIyMCIgY3k9IjIwIiByPSI0IiBmaWxsPSIjMDAwIi8+CjxjaXJjbGUgY3g9IjQ0IiBjeT0iMjAiIHI9IjQiIGZpbGw9IiMwMDAiLz4KPGNpcmNsZSBjeD0iMjAiIGN5PSI0NCIgcj0iNCIgZmlsbD0iIzAwMCIvPgo8Y2lyY2xlIGN4PSI0NCIgY3k9IjQ0IiByPSI0IiBmaWxsPSIjMDAwIi8+Cjwvc3ZnPgo=',
        5: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggOEg1NlY1Nkg4VjhaIiBmaWxsPSIjRkZGRkZGIiBzdHJva2U9IiMwMDAiIHN0cm9rZS13aWR0aD0iMiIvPgo8Y2lyY2xlIGN4PSIyMCIgY3k9IjIwIiByPSI0IiBmaWxsPSIjMDAwIi8+CjxjaXJjbGUgY3g9IjQ0IiBjeT0iMjAiIHI9IjQiIGZpbGw9IiMwMDAiLz4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iNCIgZmlsbD0iIzAwMCIvPgo8Y2lyY2xlIGN4PSIyMCIgY3k9IjQ0IiByPSI0IiBmaWxsPSIjMDAwIi8+CjxjaXJjbGUgY3g9IjQ0IiBjeT0iNDQiIHI9IjQiIGZpbGw9IiMwMDAiLz4KPC9zdmc+Cg==',
        6: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTggOEg1NlY1Nkg4VjhaIiBmaWxsPSIjRkZGRkZGIiBzdHJva2U9IiMwMDAiIHN0cm9rZS13aWR0aD0iMiIvPgo8Y2lyY2xlIGN4PSIyMCIgY3k9IjE4IiByPSI0IiBmaWxsPSIjMDAwIi8+CjxjaXJjbGUgY3g9IjQ0IiBjeT0iMTgiIHI9IjQiIGZpbGw9IiMwMDAiLz4KPGNpcmNsZSBjeD0iMjAiIGN5PSIzMiIgcj0iNCIgZmlsbD0iIzAwMCIvPgo8Y2lyY2xlIGN4PSI0NCIgY3k9IjMyIiByPSI0IiBmaWxsPSIjMDAwIi8+CjxjaXJjbGUgY3g9IjIwIiBjeT0iNDYiIHI9IjQiIGZpbGw9IiMwMDAiLz4KPGNpcmNsZSBjeD0iNDQiIGN5PSI0NiIgcj0iNCIgZmlsbD0iIzAwMCIvPgo8L3N2Zz4K'
      };
      return diceImages[value] || diceImages[1];
    },
    // 开始骰子滚动动画
    startDiceRolling() {
      this.isRolling = true;
      // 滚动动画持续2秒
      setTimeout(() => {
        this.isRolling = false;
      }, 2000);
    },
    // 获取投注项的显示名称
    getItemDisplayName(type) {
      // 从赔率列表中查找对应的显示名称
      if (Array.isArray(this.lottery_peilv_list)) {
        const item = this.lottery_peilv_list.find(item => item.type === type);
        return item ? this.$mapKoreanReactive(item.name) : type;
      } else if (this.lottery_peilv_list && typeof this.lottery_peilv_list === 'object') {
        const item = Object.values(this.lottery_peilv_list).find(item => item.type === type);
        return item ? this.$mapKoreanReactive(item.name) : type;
      }
      return type;
    },
    choosePlay(type,name){
        if(this.choose[type] === true){
          // 取消选择
          this.choose[type] = false;
          for(var i = 0;i<this.formData.length;i++){
            if(type === this.formData[i]['type'] ){
                this.formData.splice(i,1)
            }
          }
        }else if(this.choose[type] === false) {
          // 检查是否已经选择了2个礼物
          if(this.formData.length >= 2) {
            this.$toast("最多只能同時選擇兩個禮物下注");
            return;
          }
          // 添加选择
          this.formData.push({'name':name, 'type':type})
          this.choose[type] = true;
        }

        if(this.formData.length === 1){
          this.shopping = true;
        }

        // 更新显示的选择内容
        if(this.formData.length >= 1){
          for(var j = 0;j < this.formData.length;j++){
            if(j === 0){
              this.shopchoose = this.formData[j]['name'];
              this.gameitem = this.formData[j]['type'];
            }else {
              this.shopchoose += ","+this.formData[j]['name'];
              this.gameitem += ","+this.formData[j]['type'];
            }
          }
        }else {
          this.shopchoose = "未選擇";
          this.gameitem = "";
          this.shopping = false;
        }

    },
    check(){
      if(!localStorage.getItem('token')){
        this.$router.push({path:'/Register'})
      }else {
        time = window.setInterval(() => {
          setTimeout(()=>{
            this.getUserInfo();
            this.getLotteryInfo();
            this.getLotteryList();
            count++;
            if(count > 5){
              clearInterval(time);
              count = 0;
            }
          },0)
        }, 300)
      }
    },
    onRefresh() {
      setTimeout(() => {
        this.$toast(this.$t("reservation.refresh"));
        this.getLotteryList();
        this.isLoading = false;
      }, 200);
    },
    getUserInfo(){
      this.$http({
        method: 'get',
        url: 'user_info'
      }).then(res=>{
        if(res.code === 200){
          this.userInfo = res.data;
        }else if(res.code ===401){
          this.$toast(res.msg);
        }
      })
    },
    getLotteryPeilv(){
      this.$http({
        method: 'post',
        data:{id:this.$route.query.id},
        url: 'lottery_get_peilv'
      }).then(res=>{
        if(res.code === 200){
          this.lottery_peilv_list = res.data;
          console.log( this.lottery_peilv_list)
          // 动态初始化choose对象
          this.choose = {};
          if(Array.isArray(res.data)) {
            res.data.forEach(item => {
              this.$set(this.choose, item.type, false);
            });
          } else {
            Object.keys(res.data).forEach(key => {
              this.$set(this.choose, res.data[key].type, false);
            });
          }

          // 确保所有投注选项都被初始化
          this.$set(this.choose, 'big', false);
          this.$set(this.choose, 'small', false);
          this.$set(this.choose, 'odd', false);
          this.$set(this.choose, 'even', false);
          this.$set(this.choose, 'butterfly_bird', false);
          this.$set(this.choose, 'butterfly_fly', false);
        }else if(res.code ===401){
          this.$toast(res.msg);
        }
      })
    },
    getLotteryList(){
      this.$http({
        method: 'get',
        data:{key:this.$route.query.key},
        url: 'lottery_get_one_list'
      }).then(res=>{
        if(res.code === 200){
          // 确保数据是数组，并去重处理，只保留最新的数据
          let data = Array.isArray(res.data) ? res.data : [];

          // 按期号去重，保留最新的记录
          const uniqueData = [];
          const seenExpects = new Set();

          // 倒序遍历，优先保留最新的记录
          for (let i = data.length - 1; i >= 0; i--) {
            const item = data[i];
            if (item && item.expect && !seenExpects.has(item.expect)) {
              seenExpects.add(item.expect);
              uniqueData.unshift(item); // 添加到数组开头，保持正序
            }
          }

          this.lottery_list = uniqueData;
          console.log('lottery_list (去重后):')
          console.log(uniqueData)
          this.getLotteryPeilv();
        }else if(res.code ===401){
          this.$toast(res.msg);
        }
      })
    },
    getLotteryInfo(){
      this.$http({
        method: 'get',
        data:{key:this.$route.query.key},
        url: 'lottery_get_info'
      }).then(res=>{
        if(res.code === 200){
          if(parseFloat(this.userInfo.money) < parseFloat(res.data.condition)){
            this.$toast(this.$t("reservation.contact_admin"));
            this.$router.push({path:'/Home'})
            return false;
          }

          // 调试信息：检查后端返回的时间数据
          console.log('后端返回的彩票信息:', res.data);
          console.log('后端返回的second字段:', res.data.second, '类型:', typeof res.data.second);

          // 检查期号是否变化，如果变化则清空已提交订单记录
          if(this.lottery.now_expect && this.lottery.now_expect !== res.data.now_expect) {
            this.submittedOrders.clear();
            console.log('期号变化，清空已提交订单记录');
          }

          this.lottery = res.data;
          this.time = res.data.second * 1000;

          console.log('前端计算的time值:', this.time, '对应分钟数:', this.time/1000/60);

          if(this.time/1000 === 59){
            this.$toast(this.$t("reservation.prize_succ")+this.lottery.now_expect);
            // 开奖时启动骰子滚动动画
            this.startDiceRolling();
            // 开奖时清空已提交订单记录，为新期号做准备
            this.submittedOrders.clear();
          }
          // 安全获取 opencode 数据
          const opencode = res.data?.opencode || [1, 1, 1];
          this.shanzi_1 = "img/lottery/shaizi/" + (opencode[0] || 1) + ".png";
          this.shanzi_2 = "img/lottery/shaizi/" + (opencode[1] || 1) + ".png";
          this.shanzi_3 = "img/lottery/shaizi/" + (opencode[2] || 1) + ".png";

          // 更新骰子emoji值
          this.dice1 = parseInt(opencode[0]) || 1;
          this.dice2 = parseInt(opencode[1]) || 1;
          this.dice3 = parseInt(opencode[2]) || 1;

          // 使用三个骰子的总和
          this.sum = this.dice1 + this.dice2 + this.dice3;
          // 三个骰子总和：3-10为小，11-18为大（蝴蝶主题）
          if(this.sum >= 11 && this.sum <=18){
            this.size = '蝴';
          }else if(this.sum >= 3 && this.sum <= 10){
            this.size = '蝶';
          }
          if(this.sum % 2 === 0){
            this.double = '蝶.寄相思';
          }else {
            this.double = '蝶.连理枝';
          }
        }else if(res.code ===401){
          this.$toast(res.msg);
        }
      })

    }
  },
  created() {
    if(!localStorage.getItem('token')){
      this.$router.push({path:'/Login'})
    }else {
        this.getUserInfo();
        this.getLotteryInfo();
        this.getLotteryList();
    }
  },
  destroyed() {
    clearInterval(time);
  }
};
</script>

<style lang='less' scoped>
@import "../../assets/css/base.css";
.nav-bar .right{
  padding-left: 8px;
  padding-right: 8px;
  color: #fff;
  font-size: 28px;
  border-radius: 10px;
  border: 2px solid #fff;
  line-height: 60px;
}
.record{
  padding-left: 20px;
  padding-right: 20px;
  background-color: #fff;
  box-shadow: 0 2px 2px 0 #cacaca;
  z-index: 1;
}
.record .period{
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: #fff;
  border-radius: 8px;
  margin-bottom: 10px;
}
.van-count-down {
  color: #fff;
  font-size: 14px;
  font-weight: bold;
}
.linear-gradient{
  width: 100%;
  height: 2px;
}
.record .recent{
  display: flex;
  flex-direction: column;
  width: 100%;
  margin: 10px 0;
  padding: 0;
}
.kuaisan-ball .left{
  justify-content: flex-start;
  min-width: 60%;
  padding: 0 20px;
}
.kuaisan-ball{
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  width: 100%;
  padding: 10px;
}

.kuaisan-ball .res-img{
  width: 90px;
  height: 90px;
  margin-right: 20px;
}
.kuaisan-ball .res-des{
  font-weight: 700;
  text-align: center;
  color: #000;
}
.kuaisan-ball .res-des.middle{
  min-width: fit-content;
  font-size: 24px;
  white-space: nowrap;
  margin-right: 10px;
  padding: 10px 15px;
  background: rgba(244, 135, 224, 0.15);
  border-radius: 18px;
  color: #333;
  font-weight: 600;
  min-height: 45px;
  display: flex;
  align-items: center;
  justify-content: center;
}
.van-icon {
  font-size: 40px;
}
.down {
  transition: all .5s;
}
.up{
  transform: rotate(180deg);
  transition: all .5s;
}
.wrapper{
  position: relative;
  flex: 1;
  overflow: hidden;
}
.options-bar{
  display: flex;
  align-items: center;
  height: calc(100% - 80px);
}
.options-bar .game {
  flex: 1;
  height: 100%;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}
.options-bar .game .tips{
  display: flex;
  align-items: center;
  height: 100px;
  padding: 0 20px;
}
.options-bar .game .tips .odds{
  flex: 1;
  font-size: 35px;
  font-weight: 500;
  color: #ff253f;
}
.options-bar .game .tips .play-tip{
  display: flex;
  align-items: center;
  height: 100%;
}
::v-deep .van-icon-more-o{
  color: #ff253f;
  font-size: 50px;
}
.options-bar .game .tips .play-tip .span-text{
  margin-left: 10px;
  font-size: 35px;
  font-weight: bolder;
  color: #ff253f;
}
.linear-gradient{
  width: 100%;
  height: 2px;
}
.sumValueTwoSides{
  display: flex;
  padding: 30px 0;
  flex-direction: row;
  align-items: center;
  justify-content: flex-start;
  flex-wrap: wrap;
}
.rectangle{
  overflow: hidden;
}
.rectangle.large{
  margin: 0 0 30px 4%;
  width: 20%;
  border-radius: 10px;
}
.rectangle .wrapper{
  position: relative;
  padding: 0 10px;
  background: #fff;
}
.rectangle .wrapper .content{
  position: absolute;
  display: flex;
  top: 0;
  left: 0;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}
.rectangle.large .wrapper{
  padding-bottom: 100%;
}
.rectangle .wrapper .content .name-text.large{
  font-size: 4vw;
}
.rectangle .wrapper .content .name-text{
  color: #7d7c7c;
  font-weight: bolder;
}
.rectangle .wrapper .content .odd-text.large{
  font-size: 25px;
  margin-top: -30px;
}
.rectangle .wrapper .content .odd-text{
  text-align: center;
  color: #ff253f;
}
.bottom-bar {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 100px;
  z-index: 2;
}
.bottom-bar .bar .left, .bottom-bar .bar{
  display: flex;
  flex-direction: row;
  align-items: center;
}
.bottom-bar .bar{
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 100px;
  background-color: #fff;
  box-shadow: 0 0 20px 0 #cacaca;
  z-index: 2;
}
.bottom-bar .bar .left, .bottom-bar .bar{
  display: flex;
  flex-direction: row;
  align-items: center;
}
.bottom-bar .bar .left .item{
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100px;
  font-size: 20px;
}
.bottom-bar .bar .left .item .text{
  font-size: 22px;
  color: #7d7c7c;
}
.jixuanico{
  font-size: 45px;
}
.bottom-bar .bar .left .line{
  width: 2px;
  height: 50px;
  background: #dadada;
}
.bottom-bar .bar .mid{
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
.bottom-bar .bar .mid .text{
  font-size: 30px;
  font-weight: 500;
  color: #000;
}
.bottom-bar .bar .mid .text.num{
  margin: 0 5px;
  color: #ff253f;
}
.bottom-bar .bar .right{
  padding: 0 30px;
  margin: 0 30px;
  color: #fff;
  background: linear-gradient(
      270deg,#e6c3a1,#7e5678);
  font-size: 40px;
  font-weight: 500;
  height: 70px;
  line-height: 70px;
  border-radius: 50px;
}
.rectangle.active .wrapper{
  background-color: #ff253f!important;
}

::v-deep .van-pull-refresh__track .van-pull-refresh__head *{
  color: #000000;
  font-size: 35px;
}
::v-deep   .van-popup {
  position: absolute;
}
::v-deep .van-overlay {
  position: absolute;
  background-color: rgb(70 67 67 / 70%);
}
::v-deep  .van-popup--top {
  top: -1px;
}
.wrapper .item{
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 10px 0;
}
.wrapper .item .left{
  width: 40%;
  font-size: 30px;
  text-align: center;
  font-weight: 500;
  color: #000;
}
.font-weight{
  font-weight: 700!important;
}
.wrapper .item .right{
  flex: 1;
  display: flex;
  font-size: 30px;
  justify-content: center;
  overflow: hidden;
  color: #000;
}
.wrapper .item .kuaisan-ball .left{
  justify-content: flex-start;
}
.wrapper .item .kuaisan-ball{
  margin-left: 20px;
  flex: 1;
  display: flex;
  align-items: center;
  gap: 8px;
}
.wrapper .item .kuaisan-ball .res-img{
  width: 60px;
  height: 60px;
  margin-right: 15px;
}
.wrapper .item .kuaisan-ball .res-des{
  font-weight: 700;
  text-align: center;
  color: #000;
}
.wrapper .item .kuaisan-ball .res-des.middle{
  min-width: fit-content;
  font-size: 12px;
  white-space: nowrap;
  padding: 2px 6px;
  margin-right: 4px;
  background: rgba(244, 135, 224, 0.1);
  border-radius: 8px;
  color: #333;
  font-weight: 600;
}
.play-type-tip{
  position: unset;
  margin: auto;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 650px;
  height: 700px;
  max-height: 50%;
  z-index: 10;
  border-radius: 30px;
  overflow: hidden;
  background-color: #fff;
  color: #000;
}
.play-type-tip .title{
  line-height: 90px;
  background: linear-gradient(
      90deg,#7e5678,#e6c3a1);
  text-align: center;
  color: #fff;
  font-size: 35px;
  font-weight: 500;
}
.mask{
  background-color: rgb(0 0 0 / 0%);
  animation-duration: 0.35s;
}
.play-type-tip .wrapper{
  height: calc(100% - 10px);
  background-color: transparent;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}
.play-type-tip .wrapper .item{
  padding: 40px 50px;
  display: flex;
  align-items: flex-start;
}
.play-type-tip .wrapper .item .van-icon{
  color: #e6c3a1;
  font-size: 60px;
}
.play-type-tip .wrapper .item .content .content-title{
  margin-top: 22px;
  font-size: 35px;
  font-weight: 500;
  color: #000;
  line-height: 0px;
}
.play-type-tip .wrapper .item .content .content-detail{
  margin-top: 5px;
  font-size: 22px;
  color: #000;
  line-height: 30px;
}
.play-type-tip .wrapper .item .content{
  flex: 1;
  margin-left: 30px;
}
.rectangle.active .wrapper{
  background-color: #ff253f!important;
}
.rectangle.active .wrapper .name-text, .rectangle.active .wrapper .odd-text{
  color: #fff!important;
}
.bottom-bar .wrapper{
  position: absolute;
  top: 10px;
  left: 0;
  right: 0;
  padding: 20px 20px 10px 20px;
  height: 230px;
  background-color: #fff;
  z-index: 1;
  box-shadow: 0 0 10px 0 #cacaca;
  transition: transform .3s cubic-bezier(.21,1.02,.55,1.01);
}
.bottom-bar .wrapper.active{
  transform: translateY(-100%);
}
.bottom-bar .wrapper .item{
  position: relative;
  display: flex;
  flex-direction: row;
  align-items: center;
  height: 65px;
}
.bottom-bar .wrapper .item .label{
  font-size: 30px;
  line-height: 30px;
  color: #000;
}
.bottom-bar .wrapper .item .bet-number{
  flex: 1;
  margin: 0 16px;
  overflow: auto;
  white-space: nowrap;
  -webkit-overflow-scrolling: touch;
  color: #ff253f;
  font-size: 30px;
  font-weight: 500;
  height: 40px;
  line-height: 40px;
}
.bottom-bar .wrapper .item .amount-wrapper{
  flex: 1;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.van-cell {
  font-size: 30px;
  line-height: 50px;
}
.bottom-bar .wrapper .item .part{
  margin-right: 20px;
}
.bottom-bar .wrapper .item .part span{
  font-size: 30px;
  vertical-align: center;
  color: #000;
}
.bottom-bar .wrapper .item .part .number{
  margin: 0 5px;
  color: #ff253f;
  font-weight: 500;
}
::v-deep .van-field__control {
  color: #ff253f;
}
.confirm-order-modal{
  position: unset;
  display: flex;
  flex-direction: column;
  margin: auto;
  padding: 0 20px 30px;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 610px;
  height: 680px;
  max-height: 50%;
  z-index: 10;
  background-color: #fff;
  border-radius: 30px;
}
.confirm-order-modal .head{
  position: relative;
  height: 80px;
}
.confirm-order-modal .head .text{
  padding: 0 20px;
  height: 30px;
  line-height: 10px;
  text-align: center;
  font-size: 35px;
  font-weight: 500;
  color: #7e5678;
}
::v-deep .confirm-order-modal .van-hairline--bottom::after {
  border-bottom-width: 2px;
}
.van-popup--center {
  border-radius: 30px;
}
.confirm-order-modal .list{
  flex: 1;
  padding: 0 10px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
}
.confirm-order-modal .list .lise-item{
  display: flex;
  flex-direction: row;
  align-items: center;
  padding: 10px 0;
}
.confirm-order-modal .list .lise-item .main{
  flex: 1;
  overflow: hidden;
}
.confirm-order-modal .list .lise-item .main .bet-name{
  color: #ff253f;
  font-size: 35px;
  font-weight: 500;
  line-height: 0px;
  word-wrap: break-word;
  word-break: break-all;
}
.confirm-order-modal .list .lise-item .main  .detail-text{
  line-height: 0px;
  font-size: 25px;
  color: #979799;
}
.confirm-order-modal .list .lise-item{
  color: #ff253f;
}
.confirm-order-modal .sub-bar{
  display: flex;
  align-items: center;
  margin-top: 30px;
  justify-content: space-around;
}
.confirm-order-modal .sub-bar .item{
  min-width: 40%;
  height: 80px;
  text-align: center;
  box-sizing: border-box;
  border-radius: 50px;
  font-size: 35px;
  font-weight: 500;
}
.confirm-order-modal .sub-bar .item.cancel-btn{
  border: 2px solid #979799;
  color: #979799;
  background-color: #fff;
}
.confirm-order-modal .sub-bar .item.sub-btn{
  background: linear-gradient(
      270deg,#e6c3a1,#7e5678);
  color: #fff;
  border: 0;
}
.next-number span{
  font-size: 35px;
  font-weight: 700;
  color: #000;
  float: right;
}

/* 骰子容器和滚动动画 */
.dice-container {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
  margin-bottom: 25px;
  min-height: 120px;
  padding: 20px;
}

.dice-container.rolling .dice-item {
  animation: diceRoll 2s ease-in-out;
}

@keyframes diceRoll {
  0% { transform: rotate(0deg) scale(1); }
  25% { transform: rotate(90deg) scale(1.1); }
  50% { transform: rotate(180deg) scale(1.2); }
  75% { transform: rotate(270deg) scale(1.1); }
  100% { transform: rotate(360deg) scale(1); }
}

/* 骰子项目容器 */
.dice-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 90px;
  height: 90px;
  background: transparent;
  border-radius: 15px;
  transition: all 0.3s ease;
  position: relative;
}

/* 骰子图像样式 */
.dice-image {
  width: 80px;
  height: 80px;
  object-fit: contain;
  filter: drop-shadow(0 4px 8px rgba(0,0,0,0.3));
}

.dice-emoji.rolling {
  animation: diceRoll 2s ease-in-out;
}

.result-info {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 15px;
  margin-top: 25px;
  flex-wrap: wrap;
  width: 100%;
  min-height: 50px;
  padding: 10px 0;
}



/* 组合选项样式 - 两行显示，每行两个 */
.combination-options {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  padding: 0 20px;
  margin-top: 20px;
  gap: 15px;
}

.rectangle.combo {
  width: calc(50% - 7.5px);
  height: 100px;
  border-radius: 15px;
  background: #fff;
  border: 2px solid #e6c3a1;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.rectangle.combo .wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 10px;
}

.rectangle.combo .content {
  text-align: center;
  width: 100%;
}

.rectangle.combo .name-text {
  font-size: 24px;
  font-weight: 700;
  color: #333;
  margin: 0;
  margin-bottom: 5px;
  line-height: 1.2;
}

.rectangle.combo .odd-text {
  font-size: 20px;
  color: #ff253f;
  font-weight: 600;
  margin: 0;
}

.rectangle.combo.active {
  background: #ff253f;
  border-color: #ff253f;
}

.rectangle.combo.active .wrapper {
  background: #ff253f;
}

.rectangle.combo.active .name-text {
  color: #fff;
}

.rectangle.combo.active .odd-text {
  color: #fff;
}

/* 蝴蝶投注选项样式 - 铺满手机屏幕 */
.butterfly-options {
  padding: 15px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 15px;
  margin: 10px 0;
  width: 100%;
  box-sizing: border-box;
}

.butterfly-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 15px;
  gap: 10px;
}

.butterfly-row:last-child {
  margin-bottom: 0;
}

.butterfly-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  flex: 1;
  height: 120px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15px;
  border: 2px solid #e6c3a1;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

.butterfly-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0,0,0,0.15);
}

.butterfly-item.active {
  background: #ff253f;
  border-color: #ff253f;
  transform: translateY(-2px);
}

.butterfly-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 8px;
  margin-bottom: 8px;
}

.butterfly-text {
  font-size: 14px;
  font-weight: 600;
  color: #333;
  text-align: center;
  line-height: 1.2;
}

.butterfly-item.active .butterfly-text {
  color: #fff;
}

/* 主播图片样式 */
.zhubo-image-container {
  width: 100%;
  margin: 20px 0;
  text-align: center;
}

.zhubo-image {
  width: 100%;
  max-width: 100%;
  height: auto;
  border-radius: 15px;
  box-shadow: 0 4px 12px rgba(0,0,0,0.1);
}

/* 移动端适配 */
@media (max-width: 480px) {
  .butterfly-options {
    padding: 10px;
    margin: 5px 0;
  }

  .butterfly-row {
    gap: 8px;
    margin-bottom: 10px;
  }

  .butterfly-item {
    height: 100px;
    min-width: 0;
  }

  .butterfly-image {
    width: 45px;
    height: 45px;
  }

  .butterfly-text {
    font-size: 12px;
    margin-top: 5px;
  }

  .zhubo-image-container {
    margin: 15px 0;
  }

  .zhubo-image {
    border-radius: 10px;
  }
}

/* 确保右上角文字样式 */
.nav-bar .right {
  color: #fff;
  font-size: 14px;
  font-weight: 500;
}

/* 期号区域样式 */
.period {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background: #fff;
  border-radius: 8px;
  margin-bottom: 10px;
}

/* 左侧主播信息 */
.anchor-info {
  display: flex;
  align-items: center;
  flex: 1;
}

.anchor-info .cover {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  margin-right: 12px;
  border: 2px solid #ff6b9d;
}

.anchor-details {
  display: flex;
  flex-direction: column;
}

.anchor-name {
  font-size: 14px;
  color: #333;
  font-weight: 500;
  margin-bottom: 4px;
}

.anchor-id {
  font-size: 16px;
  font-weight: bold;
  color: #ff6b9d;
}

/* 右侧PK截止信息 */
.pk-deadline {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px 12px;
  background: #ff6b9d;
  border-radius: 8px;
  color: white;
}

.pk-title {
  font-size: 12px;
  margin-bottom: 4px;
  color: #000;
}

.countdown {
  font-size: 14px;
  font-weight: bold;
  color: #000;
}

/* PK结果显示样式 */
.pk-result-display {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px;
  background: #fff;
  border-radius: 8px;
  border: 2px solid #ff6b9d;
}

.pk-info {
  text-align: left;
}

.pk-result-text {
  font-size: 14px;
  color: #000;
  margin-bottom: 6px;
}

.pk-result-text .highlight {
  color: #000;
  font-weight: bold;
}

.pk-winner-text {
  font-size: 14px;
  color: #000;
}

.winner-name {
  color: #ff6b9d;
  font-weight: bold;
}

/* 赔率显示优化 */
.odd-text {
  opacity: 1 !important;
  font-weight: 500;
}

/* 开奖记录标题样式 */
.wrapper .header-item .right {
  display: flex !important;
  justify-content: center !important;
  align-items: center;
}

.ganadores-title {
  text-align: center;
  font-weight: bold;
  color: #333;
  font-size: 30px;
}

/* 开奖记录中的小骰子样式 */
.history-result {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
}

.result-display-vertical {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.dice-row {
  display: flex;
  gap: 3px;
  justify-content: center;
}

.small-dice {
  width: 25px !important;
  height: 25px !important;
  border-radius: 4px;
  object-fit: contain;
  filter: drop-shadow(0 2px 4px rgba(0,0,0,0.2));
}

.result-text {
  display: flex;
  gap: 4px;
  justify-content: center;
  flex-wrap: wrap;
}

.res-des.compact {
  font-size: 11px;
  padding: 2px 4px;
  background: rgba(244, 135, 224, 0.1);
  border-radius: 8px;
  white-space: nowrap;
  min-width: fit-content;
  text-align: center;
}

/* 确保开奖记录右侧有足够空间显示文字 */
.kuaisan-ball.history-result {
  justify-content: flex-start;
  width: 100%;
}

/* 调整开奖记录项的布局 */
.wrapper .lottery-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.wrapper .lottery-item .right {
  flex: 1;
  display: flex;
  justify-content: center;
}

/* PK结果显示样式 */
.pk-result-display {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 12px 15px;
  background: #fff;
  border-radius: 8px;
  border: 2px solid #ff6b9d;
  cursor: pointer;
  min-height: 60px;
  margin-bottom: 5px;
}

.pk-info {
  flex: 1;
}

.pk-result-text {
  font-size: 16px;
  color: #000;
  margin-bottom: 4px;
  line-height: 1.2;
}

.highlight {
  color: #000;
  font-weight: bold;
}

.pk-winner-text {
  font-size: 16px;
  color: #000;
  line-height: 1.2;
}

.winner-name {
  color: #000;
  font-weight: bold;
}

.pk-result-display .van-icon {
  font-size: 18px;
  color: #ff6b9d;
  transition: transform 0.3s ease;
  margin-left: 10px;
  flex-shrink: 0;
}

.pk-result-display .van-icon.up {
  transform: rotate(180deg);
}

.pk-result-display .van-icon.down {
  transform: rotate(0deg);
}

/* 历史记录区域样式 */
.history-section {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
  max-height: 0;
  transition: max-height 0.3s ease;
  border: 1px solid #e0e0e0;
}

.history-section.active {
  max-height: 400px;
}

.history-header {
  display: flex;
  background: #ff6b9d;
  color: white;
  padding: 10px 15px;
  font-weight: bold;
  font-size: 14px;
}

.header-item {
  flex: 1;
  text-align: center;
}

.history-list {
  max-height: 350px;
  overflow-y: auto;
}

.history-item {
  display: flex;
  padding: 10px 15px;
  border-bottom: 1px solid #f0f0f0;
  font-size: 14px;
  min-height: 40px;
  align-items: center;
}

.history-item:last-child {
  border-bottom: none;
}

.history-code {
  flex: 1;
  text-align: center;
  color: #666;
}

.history-result {
  flex: 1;
  text-align: center;
  color: #333;
}
</style>
