import Vue from 'vue'
import VueRouter from 'vue-router'
//首页
import Home from '../pages/home/<USER>'/* 首页 */
import Mine from '../pages/mine/index.vue'/* 我的 */
import Choose from '../pages/choose/index.vue'/* 选妃 */
import List from '../pages/choose/list.vue'/* 选妃列表 */
import Profile from '../pages/choose/profile.vue'/* 选妃详情 */
import Video from '../pages/video/index.vue'/* 视频 */
import Game from '../pages/game/index.vue'/* 游戏 */
import Login from '../pages/login/index.vue'/* 登录 */
import Register from '../pages/login/register.vue'/* 注册 */
import Auth from '../pages/auth/index.vue'/* 登录注册选择页 */
import ServiceOnline from '../pages/mine/ServiceOnline.vue'/* 客服列表 */
import ServicePage from '../pages/mine/ServicePage.vue'/* 客服详情界面 */
import Setting from '../pages/mine/Setting.vue'/* 设置 */
import Infomation from '../pages/mine/Infomation.vue'/* 基本信息 */
import Setname from '../pages/mine/Setname.vue'/* 修改姓名 */
import Language from '../pages/mine/Language.vue'/* 语言选择 */
import Setsex from '../pages/mine/Setsex.vue'/* 修改姓名 */
import Recharge from '../pages/mine/Recharge.vue'/* 充值 */
import SetPayPassword from '../pages/mine/SetPayPassword.vue'/* 修改提现密码 */
import SetLoginPassword from '../pages/mine/SetLoginPassword.vue'/* 修改提现密码 */
import Lottery from '../pages/lottery/index.vue'/* 彩票详情 */
import Notice from '../pages/mine/Notice.vue'/* 公告 */
import PlayVideo  from '../pages/video/PlayVideo'/* 视频播放页面 */
import Setbank  from '../pages/mine/Setbank'/* 视频播放页面 */
import BindCard  from '../pages/mine/BindCard'/* 绑定银行卡界面 */
import Withdraw  from '../pages/mine/Withdraw'/* 绑定银行卡界面 */
import Personalreport  from '../pages/mine/Personalreport'/* 个人表报 */
import GameRecord  from '../pages/mine/GameRecord'/* 游戏记录 */
import WithdrawRecord  from '../pages/mine/WithdrawRecord'/* 提现记录 */
import Account  from '../pages/mine/Account'/* 账户详情 */


Vue.use(VueRouter)
const routes = [
    {path:'/',redirect:'/Home',component:Home,meta:{title:'Citas VIP'}},
    {path:'/Home',name:'home',component:Home,meta:{title:'Citas VIP'}},
	{path:'/Choose',name:'choose',component:Choose,meta:{title:'Citas VIP - Canal de Citas'}},
	{path:'/List',name:'list',component:List,meta:{title:'Citas VIP - Lista'}},
	{path:'/Profile',name:'profile',component:Profile,meta:{title:'Citas VIP - Perfil'}},
    {path:'/Mine',name:'mine',component:Mine,meta:{title:'Citas VIP - Mi Cuenta'}},
    {path:'/Video',name:'video',component:Video,meta:{title:'Citas VIP - Videos'}},
    {path:'/Game',name:'game',component:Game,meta:{title:'Citas VIP - Juegos'}},
    {path:'/Auth',name:'auth',component:Auth,meta:{title:'Citas VIP - 登錄註冊'}},
    {path:'/Login',name:'login',component:Login,meta:{title:'Citas VIP - Iniciar Sesión'}},
    {path:'/Register',name:'register',component:Register,meta:{title:'Citas VIP - Registro'}},
    {path:'/ServiceOnline',name:'ServiceOnline',component:ServiceOnline,meta:{title:'Citas VIP - Soporte'}},
    {path:'/ServicePage',name:'ServicePage',component:ServicePage,meta:{title:'Citas VIP - Soporte'}},
    {path:'/Setting',name:'Setting',component:Setting,meta:{title:'Citas VIP - Configuración'}},
    {path:'/Infomation',name:'Infomation',component:Infomation,meta:{title:'Citas VIP - Información'}},
    {path:'/Setname',name:'Setname',component:Setname,meta:{title:'Citas VIP - Cambiar Nombre'}},
    {path:'/Setsex',name:'Setsex',component:Setsex,meta:{title:'Citas VIP - Cambiar Género'}},
    {path:'/Language',name:'Language',component:Language,meta:{title:'Citas VIP - Idioma'}},
    {path:'/Recharge',name:'Recharge',component:Recharge,meta:{title:'Citas VIP - Recargar'}},
    {path:'/SetPayPassword',name:'SetPayPassword',component:SetPayPassword,meta:{title:'Citas VIP - Contraseña'}},
    {path:'/SetLoginPassword',name:'SetLoginPassword',component:SetLoginPassword,meta:{title:'Citas VIP - Contraseña Login'}},
    {path:'/Lottery',name:'Lottery',component:Lottery,meta:{title:'Citas VIP - Lotería'}},
    {path:'/Notice',name:'Notice',component:Notice,meta:{title:'Citas VIP - Avisos'}},
    {path:'/PlayVideo',name:'PlayVideo',component:PlayVideo,meta:{title:'Citas VIP - Reproducir'}},
    {path:'/Setbank',name:'Setbank',component:Setbank,meta:{title:'Citas VIP - Banco'}},
    {path:'/BindCard',name:'BindCard',component:BindCard,meta:{title:'Citas VIP - Vincular Tarjeta'}},
    {path:'/Withdraw',name:'Withdraw',component:Withdraw,meta:{title:'Citas VIP - Retirar'}},
    {path:'/Personalreport',name:'Personalreport',component:Personalreport,meta:{title:'Citas VIP - Perfil Empresa'}},
    {path:'/WithdrawRecord',name:'WithdrawRecord',component:WithdrawRecord,meta:{title:'Citas VIP - Historial'}},
    {path:'/GameRecord',name:'GameRecord',component:GameRecord,meta:{title:'Citas VIP - Misiones'}},
    {path:'/Account',name:'Account',component:Account,meta:{title:'Citas VIP - Cuenta'}},


];

//生成路由实例
const router = new VueRouter({
    routes
})
router.beforeEach((to,from,next) => {         //修改标题路由配置加上这个
    document.title = to.matched[0].meta.title
    next()
})

export default router