/**
 * Vue插件：韩文内容映射
 * 提供全局方法来映射韩文内容为各种语言
 */

import { mapKoreanContent, mapKoreanContentInData, addKoreanMapping, addKoreanMappings } from '../utils/koreanMapping';

const KoreanMappingPlugin = {
  install(Vue) {
    // 添加全局方法
    Vue.prototype.$mapKorean = function(koreanText, targetLang = null) {
      // 如果没有指定目标语言，使用当前语言设置
      const lang = targetLang || localStorage.getItem('lang') || 'es_spa';
      return mapKoreanContent(koreanText, lang);
    };

    Vue.prototype.$mapKoreanData = function(data, targetLang = null) {
      // 如果没有指定目标语言，使用当前语言设置
      const lang = targetLang || localStorage.getItem('lang') || 'es_spa';
      return mapKoreanContentInData(data, lang);
    };

    Vue.prototype.$addKoreanMapping = function(koreanText, translations) {
      return addKoreanMapping(koreanText, translations);
    };

    Vue.prototype.$addKoreanMappings = function(mappings) {
      return addKoreanMappings(mappings);
    };

    // 添加全局过滤器
    Vue.filter('mapKorean', function(value, targetLang = null) {
      if (!value) return '';
      const lang = targetLang || localStorage.getItem('lang') || 'zh_tw';
      return mapKoreanContent(value, lang);
    });

    // 添加全局混入，提供响应式的韩文映射
    Vue.mixin({
      computed: {
        $currentLang() {
          return this.$i18n ? this.$i18n.locale : (localStorage.getItem('lang') || 'zh_tw');
        }
      },
      methods: {
        // 响应式韩文映射方法
        $mapKoreanReactive(koreanText) {
          return mapKoreanContent(koreanText, this.$currentLang);
        },
        $mapKoreanDataReactive(data) {
          return mapKoreanContentInData(data, this.$currentLang);
        }
      }
    });
  }
};

export default KoreanMappingPlugin;
