
export default{
    isLogin:function(_this){
        if(!_this.$store.getters.getLoginValue){
            return _this.$router.push("Auth")
        }
    },
    // 处理图片 URL 的全局函数
    getImageUrl(iconPath) {
        if (!iconPath) {
            return '/img/null.png'; // 默认图片
        }

        // 如果是HTTP链接，转换为HTTPS以避免混合内容警告
        if (iconPath.startsWith('http://')) {
            return iconPath.replace('http://', 'https://');
        }

        // 如果已经是HTTPS URL，直接返回
        if (iconPath.startsWith('https://')) {
            return iconPath;
        }

        // 如果是相对路径，拼接到 jsdao.cc 域名
        if (iconPath.startsWith('/')) {
            return `https://jsdao.cc${iconPath}`;
        }

        // 其他情况，假设是相对于根目录的路径
        return `https://jsdao.cc/${iconPath}`;
    },

    // 处理视频 URL 的专用函数
    getVideoUrl(videoPath) {
        if (!videoPath) {
            return null;
        }

        // 如果是HTTP链接，转换为HTTPS以避免混合内容警告
        if (videoPath.startsWith('http://')) {
            return videoPath.replace('http://', 'https://');
        }

        // 如果已经是HTTPS URL，直接返回
        if (videoPath.startsWith('https://')) {
            return videoPath;
        }

        // 如果是相对路径，拼接到 jsdao.cc 域名
        if (videoPath.startsWith('/')) {
            return `https://jsdao.cc${videoPath}`;
        }

        // 其他情况，假设是相对于根目录的路径
        return `https://jsdao.cc/${videoPath}`;
    },

    // 检测视频类型的函数
    getVideoType(videoUrl) {
        if (!videoUrl) {
            return "video/mp4"; // 默认MP4
        }

        const url = videoUrl.toLowerCase();

        // 检测MP4格式
        if (url.includes('.mp4')) {
            return "video/mp4";
        }
        // 检测WebM格式
        else if (url.includes('.webm')) {
            return "video/webm";
        }
        // 检测OGG格式
        else if (url.includes('.ogg') || url.includes('.ogv')) {
            return "video/ogg";
        }
        // 检测HLS流
        else if (url.includes('.m3u8')) {
            return "application/x-mpegURL";
        }
        // 检测DASH流
        else if (url.includes('.mpd')) {
            return "application/dash+xml";
        }
        // 默认返回MP4
        else {
            return "video/mp4";
        }
    }
}
