import Vue from 'vue';
import App from './App.vue';
import Vant from 'vant';
import 'vant/lib/index.css';
import router from './router';
import http from './http';
import VueAwesomeSwiper from 'vue-awesome-swiper'
import common from './common/function'
import 'swiper/swiper-bundle.css'
import store from './store'
import VueI18n from 'vue-i18n';
import KoreanMappingPlugin from './plugins/koreanMapping'

import 'video.js/dist/video-js.css'

Vue.prototype.$http = http
Vue.prototype.common = common
Vue.config.productionTip = false
Vue.use(VueAwesomeSwiper, /* { default options with global component } */)
Vue.use(Vant);
Vue.use(VueI18n);
Vue.use(KoreanMappingPlugin);
localStorage.setItem('lang','zh_cn')
const i18n = new VueI18n({
  globalInjection: true,
  locale: "zh_tw", // 前端显示繁体中文
  messages: {
    'zh_tw': require('./assets/languages/zh_tw.json'),
    'es_spa': require('./assets/languages/es_spa.json'),
    'zh_cn': require('./assets/languages/zh_cn.json')
  }
});
new Vue({
  store,
  router,
  i18n,
  render: h => h(App),
}).$mount('#app')
