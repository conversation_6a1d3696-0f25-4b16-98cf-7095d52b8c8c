/**
 * 韩文内容映射配置
 * 用于将后端API返回的韩文内容映射为各种语言版本
 */

export const koreanContentMapping = {
  // 注意：游戏术语 "예쁜", "섹시한", "귀여운", "매력적인" 已移除映射
  // 这些术语将保持原样显示，不进行语言转换
  
  // 时间相关韩文映射
  "분": {
    "zh_tw": "分鐘",
    "zh_cn": "分钟",
    "ko_hy": "분",
    "en_us": "min",
    "es_spa": "minutos",
    "ms_my": "min",
    "idn_yu": "menit",
    "yn_yu": "phút"
  },
  "5 분": {
    "zh_tw": "5 分鐘",
    "zh_cn": "5 分钟",
    "ko_hy": "5 분",
    "en_us": "5 min",
    "es_spa": "5 minutos",
    "ms_my": "5 min",
    "idn_yu": "5 menit",
    "yn_yu": "5 phút"
  },
  "3 분": {
    "zh_tw": "3 分鐘",
    "zh_cn": "3 分钟",
    "ko_hy": "3 분",
    "en_us": "3 min",
    "es_spa": "3 minutos",
    "ms_my": "3 min",
    "idn_yu": "3 menit",
    "yn_yu": "3 phút"
  },
  "1 분": {
    "zh_tw": "1 分鐘",
    "zh_cn": "1 分钟",
    "ko_hy": "1 분",
    "en_us": "1 min",
    "es_spa": "1 minuto",
    "ms_my": "1 min",
    "idn_yu": "1 menit",
    "yn_yu": "1 phút"
  },
  "10 분": {
    "zh_tw": "10 分鐘",
    "zh_cn": "10 分钟",
    "ko_hy": "10 분",
    "en_us": "10 min",
    "es_spa": "10 minutos",
    "ms_my": "10 min",
    "idn_yu": "10 menit",
    "yn_yu": "10 phút"
  },

  // 带括号的时间格式
  "【5 분】": {
    "zh_tw": "【5 分鐘】",
    "zh_cn": "【5 分钟】",
    "ko_hy": "【5 분】",
    "en_us": "【5 min】",
    "es_spa": "【5 minutos】",
    "ms_my": "【5 min】",
    "idn_yu": "【5 menit】",
    "yn_yu": "【5 phút】"
  },
  "【3 분】": {
    "zh_tw": "【3 分鐘】",
    "zh_cn": "【3 分钟】",
    "ko_hy": "【3 분】",
    "en_us": "【3 min】",
    "es_spa": "【3 minutos】",
    "ms_my": "【3 min】",
    "idn_yu": "【3 menit】",
    "yn_yu": "【3 phút】"
  },
  "【1 분】": {
    "zh_tw": "【1 分鐘】",
    "zh_cn": "【1 分钟】",
    "ko_hy": "【1 분】",
    "en_us": "【1 min】",
    "es_spa": "【1 minuto】",
    "ms_my": "【1 min】",
    "idn_yu": "【1 menit】",
    "yn_yu": "【1 phút】"
  },

  // 带英文方括号的时间格式
  "[5 분]": {
    "zh_tw": "[5 分鐘]",
    "zh_cn": "[5 分钟]",
    "ko_hy": "[5 분]",
    "en_us": "[5 min]",
    "es_spa": "[5 minutos]",
    "ms_my": "[5 min]",
    "idn_yu": "[5 menit]",
    "yn_yu": "[5 phút]"
  },
  "[3 분]": {
    "zh_tw": "[3 分鐘]",
    "zh_cn": "[3 分钟]",
    "ko_hy": "[3 분]",
    "en_us": "[3 min]",
    "es_spa": "[3 minutos]",
    "ms_my": "[3 min]",
    "idn_yu": "[3 menit]",
    "yn_yu": "[3 phút]"
  },
  "[1 분]": {
    "zh_tw": "[1 分鐘]",
    "zh_cn": "[1 分钟]",
    "ko_hy": "[1 분]",
    "en_us": "[1 min]",
    "es_spa": "[1 minuto]",
    "ms_my": "[1 min]",
    "idn_yu": "[1 menit]",
    "yn_yu": "[1 phút]"
  },
  "[10 분]": {
    "zh_tw": "[10 分鐘]",
    "zh_cn": "[10 分钟]",
    "ko_hy": "[10 분]",
    "en_us": "[10 min]",
    "es_spa": "[10 minutos]",
    "ms_my": "[10 min]",
    "idn_yu": "[10 menit]",
    "yn_yu": "[10 phút]"
  },

  // 修复后端错误翻译的格式
  "5 minuto": {
    "zh_tw": "5 分鐘",
    "zh_cn": "5 分钟",
    "ko_hy": "5 분",
    "en_us": "5 min",
    "es_spa": "5 minutos",
    "ms_my": "5 min",
    "idn_yu": "5 menit",
    "yn_yu": "5 phút"
  },
  "3 minuto": {
    "zh_tw": "3 分鐘",
    "zh_cn": "3 分钟",
    "ko_hy": "3 분",
    "en_us": "3 min",
    "es_spa": "3 minutos",
    "ms_my": "3 min",
    "idn_yu": "3 menit",
    "yn_yu": "3 phút"
  },
  "1 minuto": {
    "zh_tw": "1 分鐘",
    "zh_cn": "1 分钟",
    "ko_hy": "1 분",
    "en_us": "1 min",
    "es_spa": "1 minuto",
    "ms_my": "1 min",
    "idn_yu": "1 menit",
    "yn_yu": "1 phút"
  },
  "10 minuto": {
    "zh_tw": "10 分鐘",
    "zh_cn": "10 分钟",
    "ko_hy": "10 분",
    "en_us": "10 min",
    "es_spa": "10 minutos",
    "ms_my": "10 min",
    "idn_yu": "10 menit",
    "yn_yu": "10 phút"
  },
  "초": {
    "zh_tw": "秒",
    "zh_cn": "秒",
    "ko_hy": "초",
    "en_us": "sec",
    "es_spa": "seg",
    "ms_my": "saat",
    "idn_yu": "detik",
    "yn_yu": "giây"
  },
  "시간": {
    "zh_tw": "小時",
    "zh_cn": "小时",
    "ko_hy": "시간",
    "en_us": "hour",
    "es_spa": "hora",
    "ms_my": "jam",
    "idn_yu": "jam",
    "yn_yu": "giờ"
  },

  // 下拉刷新相关韩文映射
  "새로고침 성공": {
    "zh_tw": "刷新成功",
    "zh_cn": "刷新成功",
    "ko_hy": "새로고침 성공",
    "en_us": "Refresh successful",
    "es_spa": "Actualización exitosa",
    "ms_my": "Muat semula berjaya",
    "idn_yu": "Refresh berhasil",
    "yn_yu": "Làm mới thành công"
  },
  "페이지 새로 고침": {
    "zh_tw": "頁面刷新",
    "zh_cn": "页面刷新",
    "ko_hy": "페이지 새로 고침",
    "en_us": "Page refresh",
    "es_spa": "Actualizar página",
    "ms_my": "Muat semula halaman",
    "idn_yu": "Refresh halaman",
    "yn_yu": "Làm mới trang"
  },

  // 游戏状态相关韩文映射
  "대기중": {
    "zh_tw": "等待中",
    "zh_cn": "等待中",
    "ko_hy": "대기중",
    "en_us": "Waiting",
    "es_spa": "Esperando",
    "ms_my": "Menunggu",
    "idn_yu": "Menunggu",
    "yn_yu": "Đang chờ"
  },
  "진행중": {
    "zh_tw": "進行中",
    "zh_cn": "进行中",
    "ko_hy": "진행중",
    "en_us": "In Progress",
    "es_spa": "En progreso",
    "ms_my": "Sedang berlangsung",
    "idn_yu": "Sedang berlangsung",
    "yn_yu": "Đang tiến hành"
  },
  "완료": {
    "zh_tw": "完成",
    "zh_cn": "完成",
    "ko_hy": "완료",
    "en_us": "Completed",
    "es_spa": "Completado",
    "ms_my": "Selesai",
    "idn_yu": "Selesai",
    "yn_yu": "Hoàn thành"
  },

  // 金额相关韩文映射
  "원": {
    "zh_tw": "港幣",
    "zh_cn": "港币",
    "ko_hy": "원",
    "en_us": "HKD",
    "es_spa": "HKD",
    "ms_my": "HKD",
    "idn_yu": "HKD",
    "yn_yu": "HKD"
  },

  // 常用操作韩文映射
  "확인": {
    "zh_tw": "確認",
    "zh_cn": "确认",
    "ko_hy": "확인",
    "en_us": "Confirm",
    "es_spa": "Confirmar",
    "ms_my": "Sahkan",
    "idn_yu": "Konfirmasi",
    "yn_yu": "Xác nhận"
  },
  "취소": {
    "zh_tw": "取消",
    "zh_cn": "取消",
    "ko_hy": "취소",
    "en_us": "Cancel",
    "es_spa": "Cancelar",
    "ms_my": "Batal",
    "idn_yu": "Batal",
    "yn_yu": "Hủy bỏ"
  },
  "저장": {
    "zh_tw": "保存",
    "zh_cn": "保存",
    "ko_hy": "저장",
    "en_us": "Save",
    "es_spa": "Guardar",
    "ms_my": "Simpan",
    "idn_yu": "Simpan",
    "yn_yu": "Lưu"
  },

  // 彩票游戏相关韩文映射
  "대": {
    "zh_tw": "大",
    "zh_cn": "大",
    "ko_hy": "대",
    "en_us": "Big",
    "es_spa": "Grande",
    "ms_my": "Besar",
    "idn_yu": "Besar",
    "yn_yu": "Lớn"
  },
  "소": {
    "zh_tw": "小",
    "zh_cn": "小",
    "ko_hy": "소",
    "en_us": "Small",
    "es_spa": "Pequeño",
    "ms_my": "Kecil",
    "idn_yu": "Kecil",
    "yn_yu": "Nhỏ"
  },
  "홀": {
    "zh_tw": "單",
    "zh_cn": "单",
    "ko_hy": "홀",
    "en_us": "Odd",
    "es_spa": "Singular",
    "ms_my": "Ganjil",
    "idn_yu": "Ganjil",
    "yn_yu": "Lẻ"
  },
  "짝": {
    "zh_tw": "雙",
    "zh_cn": "双",
    "ko_hy": "짝",
    "en_us": "Even",
    "es_spa": "Par",
    "ms_my": "Genap",
    "idn_yu": "Genap",
    "yn_yu": "Chẵn"
  },

  // 游戏结果相关韩文映射
  "당첨": {
    "zh_tw": "中獎",
    "zh_cn": "中奖",
    "ko_hy": "당첨",
    "en_us": "Win",
    "es_spa": "Ganar",
    "ms_my": "Menang",
    "idn_yu": "Menang",
    "yn_yu": "Thắng"
  },
  "실패": {
    "zh_tw": "失敗",
    "zh_cn": "失败",
    "ko_hy": "실패",
    "en_us": "Lose",
    "es_spa": "Perder",
    "ms_my": "Kalah",
    "idn_yu": "Kalah",
    "yn_yu": "Thua"
  },
  "성공": {
    "zh_tw": "成功",
    "zh_cn": "成功",
    "ko_hy": "성공",
    "en_us": "Success",
    "es_spa": "Éxito",
    "ms_my": "Berjaya",
    "idn_yu": "Berhasil",
    "yn_yu": "Thành công"
  },

  // 数字相关韩文映射
  "첫번째": {
    "zh_tw": "第一",
    "zh_cn": "第一",
    "ko_hy": "첫번째",
    "en_us": "First",
    "es_spa": "Primero",
    "ms_my": "Pertama",
    "idn_yu": "Pertama",
    "yn_yu": "Đầu tiên"
  },
  "두번째": {
    "zh_tw": "第二",
    "zh_cn": "第二",
    "ko_hy": "두번째",
    "en_us": "Second",
    "es_spa": "Segundo",
    "ms_my": "Kedua",
    "idn_yu": "Kedua",
    "yn_yu": "Thứ hai"
  },
  "세번째": {
    "zh_tw": "第三",
    "zh_cn": "第三",
    "ko_hy": "세번째",
    "en_us": "Third",
    "es_spa": "Tercero",
    "ms_my": "Ketiga",
    "idn_yu": "Ketiga",
    "yn_yu": "Thứ ba"
  }
};

/**
 * 映射韩文内容为指定语言
 * @param {string} koreanText - 韩文文本
 * @param {string} targetLang - 目标语言代码 (zh_tw, zh_cn, en_us, etc.)
 * @returns {string} - 映射后的文本，如果没有找到映射则返回原文本
 */
export function mapKoreanContent(koreanText, targetLang = 'zh_tw') {
  if (!koreanText || typeof koreanText !== 'string') {
    return koreanText;
  }

  // 添加调试信息
  console.log('韩文映射调试 - 输入文本:', koreanText, '目标语言:', targetLang);

  // 直接查找完整匹配
  if (koreanContentMapping[koreanText] && koreanContentMapping[koreanText][targetLang]) {
    console.log('韩文映射调试 - 找到完整匹配:', koreanContentMapping[koreanText][targetLang]);
    return koreanContentMapping[koreanText][targetLang];
  }

  // 智能匹配时间格式 (数字 + 분)
  const timePattern = /^(\d+)\s*분$/;
  const timeMatch = koreanText.match(timePattern);
  if (timeMatch) {
    const number = timeMatch[1];
    const timeUnits = {
      'zh_tw': '分鐘',
      'zh_cn': '分钟',
      'ko_hy': '분',
      'en_us': 'min',
      'es_spa': 'minutos',
      'ms_my': 'min',
      'idn_yu': 'menit',
      'yn_yu': 'phút'
    };
    return `${number} ${timeUnits[targetLang] || timeUnits['zh_tw']}`;
  }

  // 智能匹配带括号的时间格式 【数字 + 분】
  const bracketTimePattern = /^【(\d+)\s*분】$/;
  const bracketTimeMatch = koreanText.match(bracketTimePattern);
  if (bracketTimeMatch) {
    const number = bracketTimeMatch[1];
    const timeUnits = {
      'zh_tw': '分鐘',
      'zh_cn': '分钟',
      'ko_hy': '분',
      'en_us': 'min',
      'es_spa': 'minutos',
      'ms_my': 'min',
      'idn_yu': 'menit',
      'yn_yu': 'phút'
    };
    return `【${number} ${timeUnits[targetLang] || timeUnits['zh_tw']}】`;
  }

  // 智能匹配带英文方括号的时间格式 [数字 + 분]
  const englishBracketTimePattern = /^\[(\d+)\s*분\]$/;
  const englishBracketTimeMatch = koreanText.match(englishBracketTimePattern);
  if (englishBracketTimeMatch) {
    const number = englishBracketTimeMatch[1];
    const timeUnits = {
      'zh_tw': '分鐘',
      'zh_cn': '分钟',
      'ko_hy': '분',
      'en_us': 'min',
      'es_spa': 'minutos',
      'ms_my': 'min',
      'idn_yu': 'menit',
      'yn_yu': 'phút'
    };
    const result = `[${number} ${timeUnits[targetLang] || timeUnits['zh_tw']}]`;
    console.log('韩文映射调试 - 英文方括号匹配成功:', result);
    return result;
  }

  // 智能匹配秒格式 (数字 + 초)
  const secondPattern = /^(\d+)\s*초$/;
  const secondMatch = koreanText.match(secondPattern);
  if (secondMatch) {
    const number = secondMatch[1];
    const secondUnits = {
      'zh_tw': '秒',
      'zh_cn': '秒',
      'ko_hy': '초',
      'en_us': 'sec',
      'es_spa': 'seg',
      'ms_my': 'saat',
      'idn_yu': 'detik',
      'yn_yu': 'giây'
    };
    return `${number} ${secondUnits[targetLang] || secondUnits['zh_tw']}`;
  }

  // 智能匹配小时格式 (数字 + 시간)
  const hourPattern = /^(\d+)\s*시간$/;
  const hourMatch = koreanText.match(hourPattern);
  if (hourMatch) {
    const number = hourMatch[1];
    const hourUnits = {
      'zh_tw': '小時',
      'zh_cn': '小时',
      'ko_hy': '시간',
      'en_us': 'hour',
      'es_spa': 'hora',
      'ms_my': 'jam',
      'idn_yu': 'jam',
      'yn_yu': 'giờ'
    };
    return `${number} ${hourUnits[targetLang] || hourUnits['zh_tw']}`;
  }

  // 智能匹配错误的西班牙语时间格式 (数字 + minuto)
  const spanishTimePattern = /^(\d+)\s*minuto$/;
  const spanishTimeMatch = koreanText.match(spanishTimePattern);
  if (spanishTimeMatch) {
    const number = spanishTimeMatch[1];
    const timeUnits = {
      'zh_tw': '分鐘',
      'zh_cn': '分钟',
      'ko_hy': '분',
      'en_us': 'min',
      'es_spa': number === '1' ? 'minuto' : 'minutos', // 西班牙语单复数规则
      'ms_my': 'min',
      'idn_yu': 'menit',
      'yn_yu': 'phút'
    };
    const result = `${number} ${timeUnits[targetLang] || timeUnits['zh_tw']}`;
    console.log('韩文映射调试 - 西班牙语时间格式匹配成功:', result);
    return result;
  }

  // 映射西班牙语彩票术语到繁体中文
  const spanishLotteryTerms = {
    'Grande': {
      'zh_tw': '大',
      'zh_cn': '大',
      'ko_hy': '대',
      'en_us': 'Big',
      'es_spa': 'Grande',
      'ms_my': 'Besar',
      'idn_yu': 'Besar',
      'yn_yu': 'Lớn'
    },
    'Pequeño': {
      'zh_tw': '小',
      'zh_cn': '小',
      'ko_hy': '소',
      'en_us': 'Small',
      'es_spa': 'Pequeño',
      'ms_my': 'Kecil',
      'idn_yu': 'Kecil',
      'yn_yu': 'Nhỏ'
    },
    'Singular': {
      'zh_tw': '單',
      'zh_cn': '单',
      'ko_hy': '홀',
      'en_us': 'Odd',
      'es_spa': 'Singular',
      'ms_my': 'Ganjil',
      'idn_yu': 'Ganjil',
      'yn_yu': 'Lẻ'
    },
    'Doble': {
      'zh_tw': '雙',
      'zh_cn': '双',
      'ko_hy': '짝',
      'en_us': 'Even',
      'es_spa': 'Doble',
      'ms_my': 'Genap',
      'idn_yu': 'Genap',
      'yn_yu': 'Chẵn'
    }
  };

  if (spanishLotteryTerms[koreanText] && spanishLotteryTerms[koreanText][targetLang]) {
    const result = spanishLotteryTerms[koreanText][targetLang];
    console.log('韩文映射调试 - 西班牙语彩票术语匹配成功:', result);
    return result;
  }

  // 如果没有找到映射，返回原文本
  console.log('韩文映射调试 - 没有找到匹配，返回原文本:', koreanText);
  return koreanText;
}

/**
 * 递归映射对象中的韩文内容
 * @param {any} data - 要处理的数据（可能是对象、数组或字符串）
 * @param {string} targetLang - 目标语言代码
 * @returns {any} - 处理后的数据
 */
export function mapKoreanContentInData(data, targetLang = 'zh_cn') {
  if (typeof data === 'string') {
    return mapKoreanContent(data, targetLang);
  }
  
  if (Array.isArray(data)) {
    return data.map(item => mapKoreanContentInData(item, targetLang));
  }
  
  if (data && typeof data === 'object') {
    const result = {};
    for (const key in data) {
      if (Object.prototype.hasOwnProperty.call(data, key)) {
        result[key] = mapKoreanContentInData(data[key], targetLang);
      }
    }
    return result;
  }
  
  return data;
}

/**
 * 游戏术语映射函数
 * @param {string} text - 韩文文本
 * @param {string} lang - 目标语言
 * @returns {string} - 映射后的文本
 */
function mapGameTerm(text, lang) {
  const gameTermMapping = {
    '예쁜': {
      'zh_cn': '大',
      'ko_hy': '대',
      'en_us': 'Big',
      'es_spa': 'Grande',
      'ms_my': 'Besar',
      'idn_yu': 'Besar',
      'yn_yu': 'Lớn'
    },
    '섹시한': {
      'zh_cn': '小',
      'ko_hy': '소',
      'en_us': 'Small',
      'es_spa': 'Pequeño',
      'ms_my': 'Kecil',
      'idn_yu': 'Kecil',
      'yn_yu': 'Nhỏ'
    },
    '귀여운': {
      'zh_cn': '双',
      'ko_hy': '짝',
      'en_us': 'Even',
      'es_spa': 'Doble',
      'ms_my': 'Genap',
      'idn_yu': 'Genap',
      'yn_yu': 'Chẵn'
    },
    '매력적인': {
      'zh_cn': '单',
      'ko_hy': '홀',
      'en_us': 'Odd',
      'es_spa': 'Singular',
      'ms_my': 'Ganjil',
      'idn_yu': 'Ganjil',
      'yn_yu': 'Lẻ'
    }
  };

  return gameTermMapping[text] && gameTermMapping[text][lang] ? gameTermMapping[text][lang] : text;
}

/**
 * 为特定API接口添加自定义韩文映射规则
 * @param {string} apiUrl - API接口URL
 * @param {object} data - API响应数据
 * @param {string} targetLang - 目标语言
 * @returns {object} - 处理后的数据
 */
export function mapSpecificApiResponse(apiUrl, data, targetLang = 'zh_cn') {
  // 根据不同的API接口应用特定的映射规则
  switch (apiUrl) {
    case '/Lottery/getLotteryInfo':
      // 彩票信息接口特殊处理 - 只处理描述字段，保护时间相关的数字字段
      if (data && typeof data === 'object') {
        // 创建一个副本来避免修改原始数据
        const result = { ...data };

        // 只处理描述字段中的韩文
        if (result.desc) {
          result.desc = mapKoreanContent(result.desc, targetLang);
        }

        // 确保时间相关字段保持原始数值类型，不进行韩文映射
        const timeFields = ['second', 'time', 'interval', 'duration'];
        timeFields.forEach(field => {
          if (data[field] !== undefined) {
            result[field] = data[field]; // 保持原始值
          }
        });

        return result;
      }
      break;

    case '/Lottery/getLotteryPeilv':
      // 彩票赔率接口特殊处理 - 将韩文游戏术语映射为传统的大小单双
      if (Array.isArray(data)) {
        data = data.map(item => {
          if (item.name) {
            item.name = mapGameTerm(item.name, targetLang);
          }
          return item;
        });
      } else if (data && typeof data === 'object') {
        Object.keys(data).forEach(key => {
          if (data[key] && data[key].name) {
            data[key].name = mapGameTerm(data[key].name, targetLang);
          }
        });
      }
      break;

    case '/Lottery/getLotteryOneList':
      // 彩票开奖列表接口特殊处理
      if (Array.isArray(data)) {
        data = data.map(item => {
          // 处理开奖结果中可能的韩文描述
          if (item.result_desc) {
            item.result_desc = mapKoreanContent(item.result_desc, targetLang);
          }
          return item;
        });
      }
      break;

    default:
      // 默认使用通用映射
      data = mapKoreanContentInData(data, targetLang);
      break;
  }

  return data;
}

/**
 * 添加新的韩文映射
 * @param {string} koreanText - 韩文文本
 * @param {object} translations - 各语言翻译对象
 */
export function addKoreanMapping(koreanText, translations) {
  koreanContentMapping[koreanText] = translations;
}

/**
 * 批量添加韩文映射
 * @param {object} mappings - 映射对象
 */
export function addKoreanMappings(mappings) {
  Object.assign(koreanContentMapping, mappings);
}

export default {
  koreanContentMapping,
  mapKoreanContent,
  mapKoreanContentInData,
  mapSpecificApiResponse,
  addKoreanMapping,
  addKoreanMappings
};
