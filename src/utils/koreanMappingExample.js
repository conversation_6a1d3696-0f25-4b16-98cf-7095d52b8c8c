/**
 * 韩文内容映射使用示例
 * 
 * 这个文件展示了如何使用韩文映射系统
 */

// 1. 在Vue组件中使用韩文映射
const exampleVueComponent = {
  template: `
    <div>
      <!-- 使用过滤器映射韩文 -->
      <p>{{ koreanText | mapKorean }}</p>
      
      <!-- 使用方法映射韩文 -->
      <p>{{ $mapKorean(koreanText) }}</p>
      
      <!-- 响应式映射（会根据语言切换自动更新） -->
      <p>{{ $mapKoreanReactive(koreanText) }}</p>
    </div>
  `,
  data() {
    return {
      koreanText: '예쁜'
    };
  },
  methods: {
    // 手动映射韩文内容
    mapContent() {
      const mapped = this.$mapKorean('섹시한', 'en_us');
      console.log(mapped); // 输出: "Sexy"
    },
    
    // 映射复杂数据结构
    mapApiResponse() {
      const apiData = {
        name: '귀여운',
        description: '매력적인',
        items: ['예쁜', '섹시한']
      };
      
      const mappedData = this.$mapKoreanData(apiData);
      console.log(mappedData);
      // 输出: { name: '可爱', description: '迷人', items: ['漂亮', '性感'] }
    }
  }
};

// 2. 在JavaScript中直接使用
import { mapKoreanContent, mapKoreanContentInData } from './koreanMapping';

// 映射单个韩文文本
const mappedText = mapKoreanContent('예쁜', 'en_us');
console.log(mappedText); // 输出: "Beautiful"

// 映射复杂数据结构
const complexData = {
  lottery: {
    result: '당첨',
    type: '대',
    description: '예쁜'
  },
  history: [
    { result: '성공', value: '섹시한' },
    { result: '실패', value: '귀여운' }
  ]
};

const mappedComplexData = mapKoreanContentInData(complexData, 'zh_cn');
console.log(mappedComplexData);
// 输出映射后的中文数据

// 3. 动态添加新的韩文映射
import { addKoreanMapping, addKoreanMappings } from './koreanMapping';

// 添加单个映射
addKoreanMapping('새로운', {
  'zh_cn': '新的',
  'ko_hy': '새로운',
  'en_us': 'New',
  'es_spa': 'Nuevo',
  'ms_my': 'Baru',
  'idn_yu': 'Baru',
  'yn_yu': 'Mới'
});

// 批量添加映射
addKoreanMappings({
  '특별한': {
    'zh_cn': '特别的',
    'ko_hy': '특별한',
    'en_us': 'Special',
    'es_spa': 'Especial',
    'ms_my': 'Istimewa',
    'idn_yu': 'Istimewa',
    'yn_yu': 'Đặc biệt'
  },
  '중요한': {
    'zh_cn': '重要的',
    'ko_hy': '중요한',
    'en_us': 'Important',
    'es_spa': 'Importante',
    'ms_my': 'Penting',
    'idn_yu': 'Penting',
    'yn_yu': 'Quan trọng'
  }
});

// 4. API响应自动映射示例
// 当调用API时，响应会自动映射韩文内容
// 例如：
// 原始API响应: { name: '예쁜', status: '성공' }
// 自动映射后: { name: '漂亮', status: '成功' } (如果当前语言是中文)

export {
  exampleVueComponent,
  mappedText,
  mappedComplexData
};
