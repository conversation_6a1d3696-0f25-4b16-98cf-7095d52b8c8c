/**
 * 韩文映射功能测试
 */

import { mapKoreanContent } from './koreanMapping';

// 测试时间映射
console.log('=== 时间映射测试 ===');
console.log('5 분 -> 中文:', mapKoreanContent('5 분', 'zh_cn')); // 应该输出: "5 分"
console.log('5 분 -> 英文:', mapKoreanContent('5 분', 'en_us')); // 应该输出: "5 min"
console.log('3 분 -> 中文:', mapKoreanContent('3 분', 'zh_cn')); // 应该输出: "3 分"
console.log('10 분 -> 越南文:', mapKoreanContent('10 분', 'yn_yu')); // 应该输出: "10 phút"

// 测试动态时间映射
console.log('\n=== 动态时间映射测试 ===');
console.log('7 분 -> 中文:', mapKoreanContent('7 분', 'zh_cn')); // 应该输出: "7 分"
console.log('15 분 -> 英文:', mapKoreanContent('15 분', 'en_us')); // 应该输出: "15 min"
console.log('30 초 -> 中文:', mapKoreanContent('30 초', 'zh_cn')); // 应该输出: "30 秒"

// 测试带括号的时间映射
console.log('\n=== 带括号时间映射测试 ===');
console.log('【5 분】 -> 中文:', mapKoreanContent('【5 분】', 'zh_cn')); // 应该输出: "【5 分】"
console.log('【5 분】 -> 英文:', mapKoreanContent('【5 분】', 'en_us')); // 应该输出: "【5 min】"
console.log('【3 분】 -> 越南文:', mapKoreanContent('【3 분】', 'yn_yu')); // 应该输出: "【3 phút】"
console.log('【7 분】 -> 中文:', mapKoreanContent('【7 분】', 'zh_cn')); // 应该输出: "【7 分】" (动态匹配)

// 测试彩票游戏映射
console.log('\n=== 彩票游戏映射测试 ===');
console.log('예쁜 -> 中文:', mapKoreanContent('예쁜', 'zh_cn')); // 应该输出: "漂亮"
console.log('섹시한 -> 英文:', mapKoreanContent('섹시한', 'en_us')); // 应该输出: "Sexy"
console.log('귀여운 -> 马来文:', mapKoreanContent('귀여운', 'ms_my')); // 应该输出: "Comel"

// 测试未映射的内容
console.log('\n=== 未映射内容测试 ===');
console.log('未知韩文 -> 中文:', mapKoreanContent('알 수 없는', 'zh_cn')); // 应该返回原文

export default {
  runTests: () => {
    console.log('韩文映射测试完成');
  }
};
