<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Citas VIP - Actividad de Dados</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #f487e0, #988fba);
            min-height: 100vh;
        }
        
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
        }
        
        .header {
            background: linear-gradient(90deg, #f487e0, #988fba);
            color: white;
            padding: 20px;
            text-align: center;
            font-size: 24px;
            font-weight: bold;
        }
        
        .game-info {
            padding: 20px;
            text-align: center;
            border-bottom: 1px solid #eee;
        }
        
        .period-info {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .dice-section {
            padding: 30px 20px;
            text-align: center;
            background: linear-gradient(to right, rgba(126, 86, 120, 0), rgb(230, 195, 161), rgba(126, 86, 120, 0));
            min-height: 200px;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }
        
        .dice-container {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 20px;
            margin: 20px 0;
            min-height: 100px;
            padding: 15px;
        }
        
        .dice-emoji {
            width: 80px;
            height: 80px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 12px;
            box-shadow: 0 3px 10px rgba(0,0,0,0.15);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 60px;
            transition: all 0.3s ease;
        }

        .dice-emoji.rolling {
            animation: diceRoll 2s ease-in-out;
        }
        
        @keyframes diceRoll {
            0% { transform: rotate(0deg) scale(1); }
            25% { transform: rotate(90deg) scale(1.1); }
            50% { transform: rotate(180deg) scale(1.2); }
            75% { transform: rotate(270deg) scale(1.1); }
            100% { transform: rotate(360deg) scale(1); }
        }
        
        .result-info-demo {
            display: flex;
            justify-content: center;
            gap: 10px;
            margin-top: 15px;
            flex-wrap: wrap;
        }

        .result-tag {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            padding: 8px 12px;
            background: rgba(244, 135, 224, 0.1);
            border-radius: 15px;
            white-space: nowrap;
            min-height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .betting-section {
            padding: 20px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        
        .basic-options {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }
        
        .combination-options {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 10px;
        }
        
        .bet-option {
            padding: 15px;
            border: 2px solid #ddd;
            border-radius: 10px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }
        
        .bet-option:hover {
            border-color: #f487e0;
            background: #f9f9f9;
        }
        
        .bet-option.active {
            border-color: #f487e0;
            background: linear-gradient(135deg, #f487e0, #988fba);
            color: white;
        }
        
        .bet-name {
            font-weight: bold;
            font-size: 16px;
            margin-bottom: 5px;
        }
        
        .bet-odds {
            font-size: 14px;
            color: #666;
        }
        
        .bet-option.active .bet-odds {
            color: rgba(255,255,255,0.9);
        }
        
        .bottom-bar {
            padding: 20px;
            background: #f5f5f5;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .balance {
            font-size: 14px;
            color: #666;
        }
        
        .submit-btn {
            background: linear-gradient(270deg, #e6c3a1, #7e5678);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
        }
        
        .demo-note {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 15px;
            margin: 20px;
            text-align: center;
            color: #856404;
        }

        /* 开奖记录样式 */
        .history-section {
            padding: 20px;
            background: #f8f9fa;
            border-top: 1px solid #eee;
        }

        .history-list {
            max-height: 300px;
            overflow-y: auto;
        }

        .history-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #eee;
        }

        .period {
            font-weight: bold;
            color: #333;
            min-width: 120px;
        }

        .result-display {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .small-dice-row {
            display: flex;
            gap: 3px;
        }

        .mini-dice {
            width: 20px;
            height: 20px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 3px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
        }

        .result-labels {
            display: flex;
            gap: 6px;
        }

        .label {
            font-size: 11px;
            padding: 2px 6px;
            background: rgba(244, 135, 224, 0.1);
            border-radius: 8px;
            white-space: nowrap;
            font-weight: 600;
            color: #333;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            Actividad 1
        </div>
        
        <div class="demo-note">
            <strong>🎲 Nueva Funcionalidad de Dados</strong><br>
            Tres dados con animación de giro + Opciones combinadas
        </div>
        
        <div class="game-info">
            <div class="period-info">
                <span>202506285252</span>
                <span style="color: #f487e0; font-weight: bold;">00:04:04</span>
            </div>
        </div>
        
        <div class="dice-section">
            <div class="dice-container">
                <div class="dice-emoji" id="dice1">⚃</div>
                <div class="dice-emoji" id="dice2">⚁</div>
                <div class="dice-emoji" id="dice3">⚄</div>
            </div>
            <div class="result-info-demo">
                <div class="result-tag">Suma: <span id="sum">14</span></div>
                <div class="result-tag">Grande</div>
                <div class="result-tag">Doble</div>
            </div>
        </div>
        
        <div class="betting-section">
            <div class="section-title">Opciones Básicas</div>
            <div class="basic-options">
                <div class="bet-option" onclick="toggleBet(this)">
                    <div class="bet-name">Grande</div>
                    <div class="bet-odds">1.98</div>
                </div>
                <div class="bet-option" onclick="toggleBet(this)">
                    <div class="bet-name">Pequeño</div>
                    <div class="bet-odds">1.98</div>
                </div>
                <div class="bet-option" onclick="toggleBet(this)">
                    <div class="bet-name">Singular</div>
                    <div class="bet-odds">1.98</div>
                </div>
                <div class="bet-option" onclick="toggleBet(this)">
                    <div class="bet-name">Doble</div>
                    <div class="bet-odds">1.98</div>
                </div>
            </div>
            
            <div class="section-title">Opciones Combinadas</div>
            <div class="combination-options">
                <div class="bet-option" onclick="toggleBet(this)">
                    <div class="bet-name">Grande Singular</div>
                    <div class="bet-odds">3.8</div>
                </div>
                <div class="bet-option" onclick="toggleBet(this)">
                    <div class="bet-name">Grande Doble</div>
                    <div class="bet-odds">3.8</div>
                </div>
                <div class="bet-option" onclick="toggleBet(this)">
                    <div class="bet-name">Pequeño Singular</div>
                    <div class="bet-odds">3.8</div>
                </div>
                <div class="bet-option" onclick="toggleBet(this)">
                    <div class="bet-name">Pequeño Doble</div>
                    <div class="bet-odds">3.8</div>
                </div>
            </div>
        </div>
        
        <div class="bottom-bar">
            <div class="balance">Saldo disponible: 0.00 MXN</div>
            <button class="submit-btn" onclick="rollDice()">Tirar Dados</button>
        </div>

        <!-- 开奖记录演示 -->
        <div class="history-section">
            <div class="section-title">Historial de Resultados</div>
            <div class="history-list">
                <div class="history-item">
                    <div class="period">202506285252</div>
                    <div class="result-display">
                        <div class="small-dice-row">
                            <div class="mini-dice">⚅</div>
                            <div class="mini-dice">⚁</div>
                            <div class="mini-dice">⚃</div>
                        </div>
                        <div class="result-labels">
                            <span class="label">12</span>
                            <span class="label">Grande</span>
                            <span class="label">Doble</span>
                        </div>
                    </div>
                </div>
                <div class="history-item">
                    <div class="period">202506285251</div>
                    <div class="result-display">
                        <div class="small-dice-row">
                            <div class="mini-dice">⚂</div>
                            <div class="mini-dice">⚁</div>
                            <div class="mini-dice">⚀</div>
                        </div>
                        <div class="result-labels">
                            <span class="label">6</span>
                            <span class="label">Pequeño</span>
                            <span class="label">Doble</span>
                        </div>
                    </div>
                </div>
                <div class="history-item">
                    <div class="period">202506285250</div>
                    <div class="result-display">
                        <div class="small-dice-row">
                            <div class="mini-dice">⚄</div>
                            <div class="mini-dice">⚃</div>
                            <div class="mini-dice">⚁</div>
                        </div>
                        <div class="result-labels">
                            <span class="label">11</span>
                            <span class="label">Grande</span>
                            <span class="label">Singular</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function toggleBet(element) {
            element.classList.toggle('active');
        }
        
        function getDiceEmoji(value) {
            const diceEmojis = ['⚀', '⚀', '⚁', '⚂', '⚃', '⚄', '⚅'];
            return diceEmojis[value] || '🎲';
        }

        function rollDice() {
            const dice1 = document.getElementById('dice1');
            const dice2 = document.getElementById('dice2');
            const dice3 = document.getElementById('dice3');

            // Agregar clase de animación
            dice1.classList.add('rolling');
            dice2.classList.add('rolling');
            dice3.classList.add('rolling');

            // Simular tirada después de la animación
            setTimeout(() => {
                const value1 = Math.floor(Math.random() * 6) + 1;
                const value2 = Math.floor(Math.random() * 6) + 1;
                const value3 = Math.floor(Math.random() * 6) + 1;

                dice1.textContent = getDiceEmoji(value1);
                dice2.textContent = getDiceEmoji(value2);
                dice3.textContent = getDiceEmoji(value3);

                const sum = value1 + value2 + value3;
                const isLarge = sum >= 11;
                const isOdd = sum % 2 === 1;

                document.getElementById('sum').textContent = sum;
                document.getElementById('size').textContent = isLarge ? 'Grande' : 'Pequeño';
                document.getElementById('parity').textContent = isOdd ? 'Singular' : 'Doble';

                // Remover clase de animación
                dice1.classList.remove('rolling');
                dice2.classList.remove('rolling');
                dice3.classList.remove('rolling');
            }, 2000);
        }
        
        // Inicializar con valores aleatorios
        rollDice();
    </script>
</body>
</html>
