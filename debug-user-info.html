<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户信息调试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .info-item {
            margin: 10px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            border-left: 4px solid #007bff;
        }
        .label {
            font-weight: bold;
            color: #333;
        }
        .value {
            color: #666;
            margin-left: 10px;
        }
        .vip-status {
            font-size: 18px;
            font-weight: bold;
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            margin: 20px 0;
        }
        .vip-yes {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .vip-no {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .error {
            color: #dc3545;
            background: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>用户信息调试页面</h1>
        
        <button onclick="getUserInfo()">获取用户信息</button>
        <button onclick="testVipStatus()">测试VIP状态</button>
        <button onclick="clearInfo()">清除信息</button>
        
        <div id="result"></div>
    </div>

    <script>
        const API_BASE = 'https://jsdao.cc/api';
        
        function getUserInfo() {
            const token = localStorage.getItem('token');
            if (!token) {
                showError('未找到登录token，请先登录');
                return;
            }
            
            fetch(`${API_BASE}/member/getUserInfo`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${token}`,
                    'Content-Type': 'application/json'
                }
            })
            .then(response => response.json())
            .then(data => {
                console.log('用户信息响应:', data);
                displayUserInfo(data);
            })
            .catch(error => {
                console.error('获取用户信息失败:', error);
                showError('获取用户信息失败: ' + error.message);
            });
        }
        
        function displayUserInfo(response) {
            const resultDiv = document.getElementById('result');
            
            if (response.code === 200) {
                const userInfo = response.data;
                const isVip = checkVipStatus(userInfo);
                
                resultDiv.innerHTML = `
                    <div class="vip-status ${isVip ? 'vip-yes' : 'vip-no'}">
                        ${isVip ? '✅ VIP会员' : '❌ 非VIP会员'}
                    </div>
                    
                    <div class="info-item">
                        <span class="label">用户名:</span>
                        <span class="value">${userInfo.username || '未设置'}</span>
                    </div>
                    
                    <div class="info-item">
                        <span class="label">VIP等级:</span>
                        <span class="value">${userInfo.vip || '无'}</span>
                    </div>
                    
                    <div class="info-item">
                        <span class="label">VIP剩余天数:</span>
                        <span class="value">${userInfo.vip_remaining_days || 0}天</span>
                    </div>
                    
                    <div class="info-item">
                        <span class="label">VIP开通时间:</span>
                        <span class="value">${userInfo.vip_open_time || '未开通'}</span>
                    </div>
                    
                    <div class="info-item">
                        <span class="label">is_see字段:</span>
                        <span class="value">${userInfo.is_see}</span>
                    </div>
                    
                    <div class="info-item">
                        <span class="label">账户状态:</span>
                        <span class="value">${userInfo.status === 1 ? '正常' : '异常'}</span>
                    </div>
                    
                    <div class="info-item">
                        <span class="label">余额:</span>
                        <span class="value">${userInfo.money || 0}</span>
                    </div>
                    
                    <h3>完整用户信息 (JSON):</h3>
                    <pre style="background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto;">
${JSON.stringify(userInfo, null, 2)}
                    </pre>
                `;
            } else {
                showError(`获取用户信息失败: ${response.msg || '未知错误'}`);
            }
        }
        
        function checkVipStatus(userInfo) {
            // 使用与前端相同的VIP判断逻辑
            if (userInfo.vip && userInfo.vip_remaining_days > 0) {
                return true;
            }
            if (userInfo.is_see === 1) {
                return true;
            }
            return false;
        }
        
        function testVipStatus() {
            const token = localStorage.getItem('token');
            if (!token) {
                showError('未找到登录token，请先登录');
                return;
            }
            
            // 模拟前端的VIP判断逻辑测试
            getUserInfo();
        }
        
        function showError(message) {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = `<div class="error">${message}</div>`;
        }
        
        function clearInfo() {
            document.getElementById('result').innerHTML = '';
        }
        
        // 页面加载时自动获取用户信息
        window.onload = function() {
            if (localStorage.getItem('token')) {
                getUserInfo();
            } else {
                showError('未找到登录token，请先在主应用中登录');
            }
        };
    </script>
</body>
</html>
