# 防重复下注功能测试指南（修复版）

## 功能说明
已修复并完善防止用户连续点击产生重复下注的机制，确保同等金额只能下注一次同一种的注单。

## 修复的问题
**原问题**: 用户提交订单后，表单被清空，重新输入相同金额和投注项时仍可以重复下注。

**修复方案**:
1. 将订单键从组合格式改为单个投注项格式
2. 为每个投注项单独生成和检查订单键
3. 即使表单清空，已提交的订单记录仍然有效

## 实现的防护机制

### 1. 提交状态锁定
- 添加 `isSubmitting` 状态变量
- 在提交过程中禁用所有相关按钮
- 显示"提交中..."状态

### 2. 精确的订单唯一性检查
- 使用 `submittedOrders` Set 记录已提交的订单
- **新的订单键格式**: `期号_单个投注项_金额`
- 例如：`20241209001_big_100`, `20241209001_small_100`
- 为每个投注项单独检查和记录

### 3. 期号变化自动清理
- 当期号变化时自动清空已提交订单记录
- 开奖时清空记录为新期号做准备

## 测试步骤

### 测试1：防止连续点击
1. 选择投注项（如：大、小）
2. 输入金额（如：100）
3. 快速连续点击"提交"按钮多次
4. **预期结果**：只有第一次点击生效，后续点击被阻止

### 测试2：相同订单防重复（修复后的核心测试）
1. 提交一个订单：大，金额100
2. 等待订单提交成功，表单自动清空
3. 重新选择相同的投注项：大，输入相同金额：100
4. 尝试提交
5. **预期结果**：显示"投注项'大'的100金额注单已存在，请勿重复下注"

### 测试3：组合投注的部分重复检查
1. 提交一个订单：大+小，金额100
2. 等待订单提交成功，表单自动清空
3. 重新选择部分相同的投注项：大，输入相同金额：100
4. 尝试提交
5. **预期结果**：显示"投注项'大'的100金额注单已存在，请勿重复下注"

### 测试4：不同金额允许下注
1. 提交一个订单：大，金额100
2. 清空选择，选择相同投注项但不同金额：大，金额200
3. 尝试提交
4. **预期结果**：允许提交，因为金额不同

### 测试5：不同投注项允许下注
1. 提交一个订单：大，金额100
2. 清空选择，选择不同投注项但相同金额：小，金额100
3. 尝试提交
4. **预期结果**：允许提交，因为投注项不同

### 测试6：期号变化后重置
1. 在当前期号提交订单
2. 等待期号变化（或手动刷新获取新期号）
3. 尝试提交相同的投注项和金额
4. **预期结果**：允许提交，因为期号已变化

## 用户体验改进

### 视觉反馈
- 提交按钮显示加载状态
- 按钮文字变为"提交中..."
- 清空按钮在提交时被禁用

### 错误提示
- "正在处理中，请稍候..." - 连续点击时
- "相同金额的注单已存在，请勿重复下注" - 重复订单时
- "下注失败，请重试" - 网络错误时

## 技术实现细节

### 修复后的订单键生成逻辑
```javascript
// 为每个投注项单独生成订单键
const currentItems = this.gameitem.split(',');
for(let item of currentItems) {
  const orderKey = `${this.lottery.now_expect}_${item.trim()}_${this.money}`;
  if(this.submittedOrders.has(orderKey)) {
    // 发现重复订单
    return false;
  }
}
```

### 修复后的订单记录逻辑
```javascript
// 提交成功后，为每个投注项单独记录
const submittedItems = this.gameitem.split(',');
for(let item of submittedItems) {
  const orderKey = `${this.lottery.now_expect}_${item.trim()}_${this.money}`;
  this.submittedOrders.add(orderKey);
}
```

### 状态管理
```javascript
// 提交前检查
if(this.isSubmitting) return false;

// 检查每个投注项是否重复
const currentItems = this.gameitem.split(',');
for(let item of currentItems) {
  const orderKey = `${this.lottery.now_expect}_${item.trim()}_${this.money}`;
  if(this.submittedOrders.has(orderKey)) return false;
}

// 提交时设置状态
this.isSubmitting = true;

// 提交完成后重置
this.isSubmitting = false;
```

### 期号变化处理
```javascript
// 检查期号变化
if(this.lottery.now_expect !== res.data.now_expect) {
  this.submittedOrders.clear();
}

// 开奖时清空
if(this.time/1000 === 59) {
  this.submittedOrders.clear();
}
```

## 注意事项
1. 此功能仅在前端实现，建议后端也添加相应的重复检查
2. 页面刷新会重置所有状态，这是正常行为
3. 不同投注项组合被视为不同订单
4. 金额必须完全相同才会被视为重复订单
